﻿<!DOCTYPE html>
<html style="min-width:550px;">
<head>
    <meta charset="utf-8">
    <title>百度云线上灰度测试插件</title>

    <style type="text/css">
    body {
        margin: 0;
        padding: 0;
    }
    a {
        color: #00f;
    }
    a:visited {
        text-decoration: none;      /*超链接无下划线*/
    }
    #helpWiki {
        display: block;
    }
    a:hover {
        text-decoration: underline; /*鼠标放上去有下划线*/
    }

    .tab {
        width: "100%";
        height: 30px;
        background: #fafafa;
        position: relative;
        margin: 0;
    }
    .tab-list li {
        width: 130px;
        height: 30px;
        list-style: none;
        line-height: 30px;
        border: 1px solid #ccc;
        text-align: center;
        float: left;
    }
    .tab-list .current {
        background-color: #c81623;
        color: #fff;
    }
    li {
        list-style-type: none;
    }
    progress {
        width: 155px;
    }

    #toggle-button {
        display: none;
    }
    .button-label {
        position: relative;
        display: inline-block;
        width: 80px;
        background-color: #ccc;
        border: 1px solid #ccc;
        border-radius: 30px;
        cursor: pointer;
    }
    .circle {
        position: absolute;
        top: 0;
        left: 0;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        background-color: #fff;
    }
    .button-label .text {
        /*
        用来阻止页面文字被选中，出现蓝色
        可以将下面两行代码注释掉来查看区别
        */
        -webkit-user-select: none;
                user-select: none;
        line-height: 30px;
        font-size: 18px;
    }
    .on {
        color: #fff;
        display: none;
        text-indent: -53px;
    }
    .off {
        color: #fff;
        display: inline-block;
        text-indent: 53px;
    }
    .button-label .circle {
        left: 0;
        transition: color .3s, border-color .3s;    /*transition过度，时间为0.3秒*/
    }

    /*
    以下是checked被选中后，紧跟checked标签后面label的样式。
    例如：div+p 选择所有紧接着<div>元素之后的<p>元素
    */
    #toggle-button:checked + label.button-label .circle {
        left: 50px;
    }
    #toggle-button:checked + label.button-label .on {
        display: inline-block;
    }
    #toggle-button:checked + label.button-label .off {
        display: none;
    }
    #toggle-button:checked + label.button-label {
        background-color: #108cee;
    }

    .table {
        border-collapse: collapse;
        border-spacing: 0;
        text-align: left;
    }
    .table-striped > tbody > tr:nth-child(odd) > td, 
    .table-striped > tbody > tr:nth-child(odd) > th {
        background-color: #f9f9f9;
    }
    .m-b-none {
        margin-bottom: 0;
    }
    .table > thead > tr > th,
    .table > tbody > tr > th,
    .table > tfoot > tr > th,
    .table > thead > tr > td,
    .table > tbody > tr > td,
    .table > tfoot > tr > td {
        padding: 8px;
        line-height: 1.428571429;
        vertical-align: top;
        border-top: 1px solid #ddd;
    }
    .text-sm {
        font-size: 12px;
    }

    </style>
</head>
<body>
<div class="tab">
    <div class="tab-list">
        <ul id="funcTab">
            <li class="drainage">分流</li>
            <li>流量抓取</li>
            <li>回放</li>
        </ul>
    </div>


    <div class="tab-con">
        <div style="text-align:center;vertical-align:middle;display:block;" class="item one">
            <table class="table table-striped m-b-none text-sm" id="funcTab1">
                <thead>
                <tr>
                    <th width="60%">参数名</th>
                    <th>参数值</th>
                </tr>
                </thead>
                <tbody>
                <tr>
                    <td><label><font color="#0000FF">FE版本</font>(<font color="#8E8E8E">示例_xxx_version_:*******，为空则使用已发布上线的Console页面</font>)</label></td>
                    <td><input type="text" id="feVersion" style="width:240px;" autofocus="autofocus" placeholder="_xxx_version_:*******"></td>
                </tr>
                <tr>
                    <td><label><font color="#FF0000">*</font><font color="#0000FF">项目标签</font>(<font color="#8E8E8E">长度必须为4、必须设置</font>)</label></td>
                    <td style="position:relative;"><input type="text" id="projectTag" style="width:240px;"><select id="projectTagSelect" style="position:absolute;width:150px;top:10px;left:96px;border-style:none;display:none"></select></td>
                </tr>
                <tr>
                    <td><label><font color="#FF0000">*</font><font color="#0000FF">环境标签</font>(<font color="#999">长度必须为4、有多灰度环境须设置</font>)</label></td>
                    <td style="position:relative;"><input type="text" id="envTag" style="width:240px;" placeholder="gray"><select id="envTagSelect" style="position:absolute;width:150px;top:10px;left:96px;border-style:none;display:none;"></select></td>
                </tr>
                <tr>
                    <td><label><font color="#FF0000">*</font><font color="#0000FF">分流标签</font>(<font color="#8E8E8E">长度必须为12、一般为bcecanarytag</font>)</label></td>
                    <td><input type="text" id="diverterTag" style="width:240px;" placeholder="bcecanarytag"></td>
                </tr>
                <tr>
                    <td><label>自动生成的X-Bce-Request-Id示例</label></td>
                    <td><input type="text" id="xBceRequestIdExample" readonly="readonly" style="width:240px;" placeholder="d53be6f3-bb3f-gray-pjid-bcecanarytag"></td>
                </tr>
                </tbody>
            </table>

            <div style="text-align:center;vertical-align:middle;">
                <input type="checkbox" id="toggle-button">
                <!--label中的for跟input的id绑定。作用是在点击label时选中input或者取消选中input-->
                <label for="toggle-button" class="button-label">
                    <span class="circle"></span>
                    <span class="text on">开</span>
                    <span class="text off">关</span>
                </label>
            </div>

            <div style="text-align:center;vertical-align:middle;">
                <a id="helpWiki" style="cursor:pointer;">各项目的FE版本格式、项目标签、环境标签和分流标签的值，请点击此处、查看wiki</a>
                <label>百度云灰度测试插件 版本号：3.0</label>
            </div>

        </div>

        <div style="text-align:center;vertical-align:middle;display:none;" class="item two">
            <table class="table table-striped m-b-none text-sm" id="funcTab2">
                <thead>
                <tr>
                    <th width="60%">引流任务参数</th>
                    <th>参数值</th>
                </tr>
                </thead>
                <tbody>

                <tr>
                    <td><label><font color="#FF0000">*</font><font color="#0000FF">项目名称</font></label></td>
                    <td style="position:relative;"><select name="ylq" id="drainageName" style="position:absolute;width:240px;top:10px;border-style:none;">
                        <option name="projectName">请选择项目</option>
                    </select></td>
                </tr>
                <tr>
                    <td><label><font color="#FF0000">*</font><font color="#0000FF">地域</font></label></td>
                    <td style="position:relative;"><select id="drainageRegion" style="position:absolute;width:240px;top:10px;border-style:none;">
                        <option value="test" name="projectName">请选择地域</option>
                    </select></td>
                </tr>

                <tr>
                    <td><label><font color="#FF0000">*</font><font color="#0000FF">抓取请求类型</font></label></td>
                    <td style="position:relative;"><select id="drainageType" style="position:absolute;width:240px;top:10px;border-style:none;">
                        <option value="test" name="projectName">请选择抓取类型</option>
                    </select></td>
                </tr>

                <tr>
                    <td><label><font color="#0000FF">抓取任务名</font>(<font color="#8E8E8E">选择项目地域后，自动生成任务名</font>)</label></td>
                    <td><label><input type="text" id="taskName" style="width:240px;" autofocus="autofocus"></label></td>
                </tr>

                </tbody>
            </table>

            <button type="button" id="drainage_start">开始流量抓取</button>&nbsp;&nbsp;&nbsp;&nbsp;
            <button type="button"  id="drainage_stop">完成流量抓取</button><br>
            <div style="text-align:center;vertical-align:middle;">
                <a id="drainageUrl" style="cursor:pointer;">您可点击此处前往amis平台查看抓取进度</a></div>
            </div>


        </div>

        <div style="text-align:center;vertical-align:middle;display:none;" class="item three"><br><br><br>
            <table class="table table-striped m-b-none text-sm" style="width:550px" id="funcTab3">
                <thead>
                <tr>
                    <th style="width:275px">回放任务参数</th>
                    <th>参数值</th>
                </tr>
                </thead>
                <tbody>


                <tr>
                    <td><label><font color="#FF0000">*</font><font color="#0000FF">项目名称</font></label></td>
                    <td style="position:relative;"><select id="replayName" style="position:absolute;width:240px;top:10px;border-style:none;">
                        <option name="projectName">请选择项目</option>
                    </select></td>
                </tr>

                <tr>
                    <td><label><font color="#FF0000">*</font><font color="#0000FF">地域</font></label></td>
                    <td style="position:relative;"><select id="replayRegion" style="position:absolute;width:240px;top:10px;border-style:none;">
                        <option value="test" name="projectName">请选择地域</option>
                    </select></td>
                </tr>

                <tr>
                    <td><label><font color="#FF0000">*</font><font color="#0000FF">回放环境</font></label></td>
                    <td style="position:relative;"><select id="replayType" style="position:absolute;width:240px;top:10px;border-style:none;">
                        <option value="test" name="projectName">请选择回放环境</option>
                    </select></td>
                </tr>


                <tr>
                    <td><label><font color="#FF0000">*</font><font color="#0000FF">回放数据集</font></label></td>
                    <td style="position:relative;"><select id="replayData" style="position:absolute;width:240px;top:10px;border-style:none;">
                        <option value="test" name="projectName">请选择需要回放的数据集</option>
                    </select></td>
                </tr>

                <tr>
                    <td><label><font color="#FF0000">*</font><font color="#0000FF">鉴权方式</font></label></td>
                    <td style="position:relative;"><select id="AuthType" style="position:absolute;width:240px;top:10px;border-style:none;">
                        <option value="baiduyun" name="baiduyun">请选择鉴权方式</option>
                        <option value="baiduyun" name="baiduyun">百度云iam鉴权</option>
                        <option value="cookie" name="cookie">cookie鉴权</option>
                        <option value="jwt" name="jwt">jwt鉴权</option>
                    </select></td>
                </tr>


                </tbody>
            </table>

            <input type="button" value="开始回放" id="replay_start">&nbsp;&nbsp;&nbsp;
            <input type="button" value="暂停回放" id="replay_stop">&nbsp;&nbsp;&nbsp;
            <input type="button" value="继续回放" id="replay_restart"><br>

            <progress class="my-progress" id="myProgress"  style="display:none;" value="0" max="100"></progress>
            <span id="mySpan" style="display:none;"></span><br>

        <div style="text-align:center;vertical-align:middle;">
            <a id="replayResultUrl" style="cursor:pointer;">请点击此处前往amis平台查看回放结果</a></div>
        </div>
    </div>
</div>

    <script type="text/javascript" charset="utf-8" src="popup.js"></script>

</body>
</html>
