/**
 * @file 百度云线上灰度测试的chrome插件弹出的处理json，V3版本使用chrome.storage.local
 * <AUTHOR>
 */

// V3版本兼容性：使用chrome.storage.local替代localStorage
const storage = {
    getItem: (key) => {
        return new Promise((resolve) => {
            chrome.storage.local.get([key], (result) => {
                resolve(result[key] || null);
            });
        });
    },
    setItem: (key, value) => {
        return new Promise((resolve) => {
            chrome.storage.local.set({ [key]: value }, resolve);
        });
    },
    removeItem: (key) => {
        return new Promise((resolve) => {
            chrome.storage.local.remove([key], resolve);
        });
    }
};

// 异步初始化函数
async function initializePopup() {
    // 设置wiki打开
    document.getElementById('helpWiki').onclick = function () {
        parent.open('http://wiki.baidu.com/pages/viewpage.action?pageId=1002931815');
    };

    // 访问amis,查看回放结果地址
    document.getElementById("replayResultUrl").onclick = async function () {
        var objS = document.getElementById("replayName");
        var project;
        if (objS.selectedIndex != 0) {
            project = objS.options[objS.selectedIndex].text;
        } else {
            project = await storage.getItem("lastReplayProject");
        }
        var objRegion = document.getElementById("replayRegion");
        var region;
        if (objRegion.selectedIndex != 0) {
            region = objRegion.options[objRegion.selectedIndex].text;
        } else {
            region = await storage.getItem("lastReplayRegion");
        }
        var objData = document.getElementById("replayData");
        var data;
        if (objData.selectedIndex != 0) {
            data = objData.options[objData.selectedIndex].text;
        } else {
            data = await storage.getItem("lastReplayData");
        }
        parent.open("http://amis.baidu.com/group/canary/canary/drainage/drainage_playback?project=" + project + "&region=" + region + "&dataId=" + data + "&page=1");
    };

    // 访问amis,查看抓取流量进度地址
    document.getElementById("drainageUrl").onclick = async function() {
        var objS = document.getElementById("drainageName");
        var project;
        if (objS.selectedIndex != 0) {
            project = objS.options[objS.selectedIndex].text;
        } else {
            project = await storage.getItem("lastDrainageProject");
        }
        var objDrainageRegion = document.getElementById("drainageRegion");
        var region;
        if (objDrainageRegion.selectedIndex != 0) {
            region = objDrainageRegion.options[objDrainageRegion.selectedIndex].text;
        } else {
            region = await storage.getItem("lastDrainageRegion");
        }
        parent.open("http://amis.baidu.com/group/canary/canary/drainage/drainage_job?project=" + project + "&region=" + region);
    };

    // canary地址,实现引流回放等任务
    var canaryEndpoint = "canary.baidu-int.com";

    // 设置开关按钮
    var toggleButton = document.getElementById('toggle-button');
    toggleButton.onchange = async function () {
        if (toggleButton.checked) {
            // 判断项目标签合法性，其长度必须为4
            var reg = /^[a-zA-Z0-9]{4}$/;
            if (! reg.test(document.getElementById('projectTag').value)) {
                toggleButton.checked = false;
                await storage.setItem('bceCanarySwitch', 'off');

                alert('非法的项目标签，其必须是字母或数字组成、长度为4的字符串');
                document.getElementById('projectTag').focus();
                return;
            }

            // 判断环境标签合法性，其长度必须为4
            reg = /^[a-zA-Z0-9]{4}$/;
            if (! reg.test(document.getElementById('envTag').value)) {
                toggleButton.checked = false;
                await storage.setItem('bceCanarySwitch', 'off');

                alert('非法的环境标签，其必须是字母或数字组成、长度为4的字符串');
                document.getElementById('envTag').focus();
                return;
            }

            // 判断分流标签合法性，其长度必须为12
            reg = /^[a-zA-Z0-9]{12}$/;
            if (! reg.test(document.getElementById('diverterTag').value)) {
                toggleButton.checked = false;
                await storage.setItem('bceCanarySwitch', 'off');

                alert('非法的分流标签，其必须是字母和数字组成的、长度为12的字符串');
                document.getElementById('diverterTag').focus();
                return;
            }

            await storage.setItem('bceCanarySwitch', 'on');
            updateXBceRequestIdExample();

        } else {
            await storage.setItem('bceCanarySwitch', 'off');
        }
    };

    // 从历史数据中开关状态
    var bceCanarySwitch = await storage.getItem('bceCanarySwitch');
    if (bceCanarySwitch === 'on') {
        toggleButton.checked = true;
    } else {
        toggleButton.checked = false;
    }

    // 每次点开插件、都更新一次数据
    var httpRequest = new XMLHttpRequest();
    httpRequest.open('GET', 'http://' + canaryEndpoint + ':8000/chrome_plugin/show_info', true);
    httpRequest.send();
    httpRequest.onreadystatechange = async function () {
        if (httpRequest.readyState == 4 && httpRequest.status == 200) {
            var resString = httpRequest.responseText;
            var resJson = JSON.parse(resString);

            // 从storage中获取之前存储的选中的项目id
            var projectTagSelectProject = await storage.getItem('projectTagSelectProject');

            // 根据json，将项目列表填充到projectTagSelect中
            var projectTagSelect = document.getElementById('projectTagSelect');
            projectTagSelect.style.display = '';
            projectTagSelect.add(new Option('', ''))
            var projectId = 1;
            var selectProjectId = 0;
            for (var eachProject in resJson) {
                projectTagSelect.add(new Option(eachProject, eachProject))

                // 找到之前选择的project tag的id
                if (eachProject === projectTagSelectProject) {
                    selectProjectId = projectId;
                }
                projectId = projectId + 1;
            }

            // 设置之前设置的项目为选中状态
            projectTagSelect.options[selectProjectId].selected = true;

            // 从storage中获取之前存储的选中的环境id
            var envTagSelectEnv = await storage.getItem('envTagSelectEnv');

            // 根据该项目的env配置，设置envTagSelect中的值
            if (selectProjectId != 0) {
                // 用服务端反馈的最新值更新storage中的历史数据
                await storage.setItem('projectTag', resJson[projectTagSelectProject]['project_tag']);
                await storage.setItem('diverterTag', resJson[projectTagSelectProject]['diverter_tag']);

                // 动态设置env的select
                var envTagSelect = document.getElementById('envTagSelect');
                envTagSelect.style.display = '';
                envTagSelect.options.length = 0;
                envTagSelect.add(new Option('', ''))
                var envId = 1;
                var selectEnvId = 0;
                for (var eachEnv in resJson[projectTagSelectProject]['env']) {
                    envTagSelect.add(new Option(eachEnv, eachEnv))

                    // 找到之前选择的env tag的id
                    if (eachEnv === envTagSelectEnv) {
                        selectEnvId = envId;

                        if (selectEnvId != 0) {
                            await storage.setItem('envTag', resJson[projectTagSelectProject]['env'][eachEnv]);
                        }
                    }
                    envId = envId + 1;
                }

                // 设置之前设置的环境为选中状态
                envTagSelect.options[selectEnvId].selected = true;
            }

            // 保存string格式的数据到storage中
            await storage.setItem('projectTagSelect', resString);

            // 从历史数据中恢复项目标签
            document.getElementById('projectTag').value = await storage.getItem('projectTag') || '';
            // 从历史数据中恢复环境标签
            document.getElementById('envTag').value = await storage.getItem('envTag') || '';
            // 从历史数据中恢复分流器标签
            document.getElementById('diverterTag').value = await storage.getItem('diverterTag') || '';
            // 更新示例显示
            updateXBceRequestIdExample();
        }
    };

    async function updateXBceRequestIdExample() {
        document.getElementById('xBceRequestIdExample').value = 'd53be6f3-bb3f-'
                                                                + document.getElementById('envTag').value
                                                                + '-'
                                                                + document.getElementById('projectTag').value
                                                                + '-'
                                                                + document.getElementById('diverterTag').value;

        await storage.setItem('projectTag', document.getElementById('projectTag').value);
        await storage.setItem('envTag', document.getElementById('envTag').value);
        await storage.setItem('diverterTag', document.getElementById('diverterTag').value);
    }

    // 项目标签选择变化处理
    document.getElementById('projectTagSelect').onchange = async function() {
        var projectTagSelect = document.getElementById('projectTagSelect');
        var projectTagSelectProject = projectTagSelect.options[projectTagSelect.selectedIndex].value;

        // 保存选中的select到storage中，用于后续恢复
        await storage.setItem('projectTagSelectProject', projectTagSelectProject);

        // 从storage中取出string格式的信息、并转换为json
        var resString = await storage.getItem('projectTagSelect');
        var resJson = JSON.parse(resString);

        if (projectTagSelect.selectedIndex == 0) {
            document.getElementById('projectTag').value = '';
            document.getElementById('diverterTag').value = '';
            document.getElementById('envTag').value = '';

            // 将该已设置的envTagSelect关闭显示，回到初始状态
            document.getElementById('envTagSelect').style.display = 'none';
        } else {
            // 根据该值，更新projectTag、diverterTag字段
            document.getElementById('projectTag').value = resJson[projectTagSelectProject]['project_tag'];
            document.getElementById('diverterTag').value = resJson[projectTagSelectProject]['diverter_tag'];

            // 根据该项目的env配置，设置envTagSelect中的值
            var envTagSelect = document.getElementById('envTagSelect');
            envTagSelect.style.display = '';
            envTagSelect.options.length = 0;
            envTagSelect.add(new Option('', ''))
            for (var eachEnv in resJson[projectTagSelectProject]['env']) {
                envTagSelect.add(new Option(eachEnv, eachEnv))
            }
        }

        // 将开关设置为关闭状态
        toggleButton.checked = false;
        await storage.setItem('bceCanarySwitch', 'off');

        // 清除之前设置的环境标签
        document.getElementById('envTag').value = '';

        updateXBceRequestIdExample();
    }

    // 环境标签选择变化处理
    document.getElementById('envTagSelect').onchange = async function() {
        var envTagSelect = document.getElementById('envTagSelect');
        var projectTagSelect = document.getElementById('projectTagSelect');

        var projectTagSelectProject = projectTagSelect.options[projectTagSelect.selectedIndex].value;
        var envTagSelectEnv = envTagSelect.options[envTagSelect.selectedIndex].value;

        // 保存选中的select到storage中，用于后续恢复
        await storage.setItem('envTagSelectEnv', envTagSelectEnv);

        // 从storage中取出string格式的信息、并转换为json
        var resString = await storage.getItem('projectTagSelect');
        var resJson = JSON.parse(resString);

        // 更新envTag字段
        if (envTagSelect.selectedIndex == 0) {
            document.getElementById('envTag').value = '';
        } else {
            document.getElementById('envTag').value = resJson[projectTagSelectProject]['env'][envTagSelectEnv];
        }

        // 将开关设置为关闭状态
        toggleButton.checked = false;
        await storage.setItem('bceCanarySwitch', 'off');

        updateXBceRequestIdExample();
    }

    // 从历史数据中恢复fe版本号
    var currentFeVersion = await storage.getItem('feVersion');
    if (currentFeVersion != null) {
        var multiProjectList = [];
        multiProjectList = currentFeVersion.split(',');
        if (multiProjectList.length > 0) {
            var currentFeVersionList = [];
            currentFeVersionList = multiProjectList[0].split(':');
            if (currentFeVersionList.length === 2) {
                if (currentFeVersionList[1] === 'delete') {
                    document.getElementById('feVersion').value = '';
                } else {
                    document.getElementById('feVersion').value = currentFeVersion;
                }
            }
        }
    }

    // 从历史数据中恢复项目标签
    document.getElementById('projectTag').value = await storage.getItem('projectTag') || '';
    // 从历史数据中恢复环境标签
    document.getElementById('envTag').value = await storage.getItem('envTag') || '';
    // 从历史数据中恢复分流器标签
    document.getElementById('diverterTag').value = await storage.getItem('diverterTag') || '';
    // 更新示例显示
    updateXBceRequestIdExample();

    // 获得界面设置的fe版本号
    document.getElementById('feVersion').onchange = async function() {
        var feVersion = document.getElementById('feVersion').value;
        if (feVersion.length === 0) {
            alert('注意：fe版本号为空，将清空之前的fe版本设置、使用已发布上线的Console');
            var oldFeVersion = await storage.getItem('feVersion');
            if ((oldFeVersion != null) && (oldFeVersion.length > 3)) {
                var multiProjectList = [];
                multiProjectList = oldFeVersion.split(',');
                // 清空feVersion缓存
                var i = 0;
                var FeVerList = [];
                while (i < multiProjectList.length) {
                    var oldFeVersionList = [];
                    oldFeVersionList = multiProjectList[i].split(':');
                    if (oldFeVersionList.length === 2) {
                        FeVerList.push(oldFeVersionList[0] + ':delete');
                    }
                    i++;
                }
                await storage.setItem('feVersion', FeVerList.join(','));
            }
        } else {
            await storage.setItem('feVersion', document.getElementById('feVersion').value);
        }
    };

    // 更新项目标签
    document.getElementById('projectTag').onchange = async function () {
        // 将project、env的select的值、设置为空
        if (document.getElementById('projectTagSelect').style.display === '') {
            document.getElementById('projectTagSelect').options[0].selected = true;
        }
        if (document.getElementById('envTagSelect').style.display === '') {
            document.getElementById('envTagSelect').options[0].selected = true;
        }
        await storage.setItem('projectTagSelectProject', '');
        await storage.setItem('envTagSelectEnv', '');

        // 先判断项目标签合法性，其长度必须为4
        var reg = /^[a-zA-Z0-9]{4}$/;
        if (! reg.test(document.getElementById('projectTag').value)) {
            toggleButton.checked = false;
            await storage.setItem('bceCanarySwitch', 'off');

            alert('非法的项目标签，其必须是字母或数字组成、长度为4的字符串');
            document.getElementById('projectTag').focus();
            return;
        }

        updateXBceRequestIdExample();
    };

    // 更新环境标签
    document.getElementById('envTag').onchange = async function () {
        // 将env select的值设置为空
        if (document.getElementById('envTagSelect').style.display === '') {
            document.getElementById('envTagSelect').options[0].selected = true;
        }
        await storage.setItem('envTagSelectEnv', '');

        // 先判断环境标签合法性，其长度必须为4
        var reg = /^[a-zA-Z0-9]{4}$/;
        if (! reg.test(document.getElementById('envTag').value)) {
            toggleButton.checked = false;
            await storage.setItem('bceCanarySwitch', 'off');

            alert('非法的环境标签，其必须是字母或数字组成、长度为4的字符串');
            document.getElementById('envTag').focus();
            return;
        }

        updateXBceRequestIdExample();
    };

    // 更新分流器tag
    document.getElementById('diverterTag').onchange = async function () {
        // 先判断分流标签合法性，其长度必须为12
        var reg = /^[a-zA-Z0-9]{12}$/;
        if (! reg.test(document.getElementById('diverterTag').value)) {
            toggleButton.checked = false;
            await storage.setItem('bceCanarySwitch', 'off');

            alert('非法的分流标签，其必须是字母或数字组成、长度为12的字符串');
            document.getElementById('diverterTag').focus();
            return;
        }

        updateXBceRequestIdExample();
    };

    // Tab切换功能
    var tab_list = document.querySelector('.tab-list');
    var lis = tab_list.querySelectorAll('li');
    // 循环绑定点击事件
    for (var i = 0; i < lis.length; i++) {
        lis[i].setAttribute('index', i);
        lis[i].onclick = async function() {
            for (var j = 0; j < lis.length; j++) {
                lis[j].className = '';
            }
            // 留下现在需要用的
            this.className = 'drainage';
            // 下面显示内容模块
            var index = this.getAttribute('index');
            await storage.setItem('selectedTab', index);
            console.log(index);
            // 去掉其他的 item，让这些隐藏起来
            // 只留下当前的 item，让它显示出来
            for (var i = 0; i < items.length; i++) {
                items[i].style.display = 'none';
            }
            items[index].style.display = 'block';
        };
    }

    // 点击选中tab栏，设置选中对象颜色状态变化
    var ul = document.getElementsByTagName("ul")[0];
    var li = document.getElementsByTagName("li");
    // 默认加载插件时 显示分流tab选中设为蓝色
    li[0].style.background = '#99CCFF';
    ul.onclick = function(e) {
        for (var i = 0; i < li.length; i++) {
            li[i].style.background = '#FFFFFF';
        }
        var temp = e.srcElement;
        temp.style.background = '#99CCFF';
    }

    // 默认显示上次操作所在tab,若之前在分流tab页面，则再次打开插件也会显示分流tab页面
    var items = document.querySelectorAll(".item");
    for (var i = 0; i < items.length; i++) {
        items[i].style.display = 'none';
    }

    const selectedTab = await storage.getItem('selectedTab');
    if (selectedTab) {
        items[selectedTab].style.display = 'block';
        // 点击选中tab栏，设置选中对象颜色状态变化
        var ul = document.getElementsByTagName("ul")[0];
        var li = document.getElementsByTagName("li");
        li[0].style.background = '#FFFFFF';
        for (var i = 0; i < li.length; i++) {
            li[i].style.background = '#FFFFFF';
        }
        li[selectedTab].style.background = '#99CCFF';
    } else {
        items[0].style.display = 'block';
    }
}

// 当DOM加载完成时初始化popup
document.addEventListener('DOMContentLoaded', function() {
    initializePopup();
});
