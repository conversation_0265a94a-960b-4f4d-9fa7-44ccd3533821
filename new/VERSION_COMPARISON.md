# V2 vs V3 版本对比

## 功能对比

| 功能 | V2版本 | V3版本 | 状态 |
|------|--------|--------|------|
| 灰度分流 | ✅ | ✅ | 完全兼容 |
| FE版本设置 | ✅ | ✅ | 完全兼容 |
| 流量抓取 | ✅ | ✅ | 完全兼容 |
| 回放功能 | ✅ | ✅ | 完全兼容 |
| Tab界面 | ✅ | ✅ | 完全兼容 |
| 配置存储 | ✅ | ✅ | 升级到chrome.storage |

## 技术架构对比

### Manifest文件
```json
// V2版本
{
  "manifest_version": 2,
  "background": {
    "scripts": ["bce_canary_plugin.js"]
  },
  "browser_action": {
    "default_popup": "popup.html"
  },
  "permissions": [
    "webRequest",
    "webRequestBlocking",
    "https://cloud.baidu.com/*",
    // ... 其他权限
  ]
}

// V3版本
{
  "manifest_version": 3,
  "background": {
    "service_worker": "bce_canary_plugin.js"
  },
  "action": {
    "default_popup": "popup.html"
  },
  "permissions": [
    "declarativeNetRequest",
    "tabs",
    "storage",
    "scripting"
  ],
  "host_permissions": [
    "https://cloud.baidu.com/*",
    // ... 其他域名
  ]
}
```

### 后台脚本架构

#### V2版本 (Background Script)
```javascript
// 持续运行的后台脚本
chrome.webRequest.onBeforeSendHeaders.addListener(
  function(details) {
    // 修改请求头
  },
  {urls: ['<all_urls>']},
  ['requestHeaders', 'blocking']
);
```

#### V3版本 (Service Worker)
```javascript
// 事件驱动的Service Worker
chrome.declarativeNetRequest.updateDynamicRules({
  addRules: [{
    id: 1,
    action: {
      type: "modifyHeaders",
      requestHeaders: [...]
    }
  }]
});
```

### 存储方式对比

#### V2版本
```javascript
// 使用localStorage
window.localStorage.setItem('key', 'value');
var value = window.localStorage.getItem('key');
```

#### V3版本
```javascript
// 使用chrome.storage.local
await chrome.storage.local.set({key: 'value'});
const result = await chrome.storage.local.get(['key']);
```

### 脚本注入对比

#### V2版本
```javascript
chrome.tabs.executeScript(tabId, {
  code: 'window.sessionStorage.setItem("key", "value");'
});
```

#### V3版本
```javascript
chrome.scripting.executeScript({
  target: {tabId: tabId},
  func: (k, v) => window.sessionStorage.setItem(k, v),
  args: [key, value]
});
```

## 性能对比

| 指标 | V2版本 | V3版本 | 改进 |
|------|--------|--------|------|
| 内存占用 | 持续占用 | 按需激活 | ✅ 更低 |
| 启动速度 | 较慢 | 更快 | ✅ 提升 |
| 安全性 | 标准 | 增强 | ✅ 更安全 |
| 权限控制 | 粗粒度 | 细粒度 | ✅ 更精确 |

## 兼容性

### 浏览器支持
- **V2版本**: Chrome 所有版本
- **V3版本**: Chrome 88+ (2021年1月发布)

### 迁移注意事项
1. **异步处理**: V3版本所有存储操作都是异步的
2. **Service Worker生命周期**: 需要处理Service Worker的休眠和唤醒
3. **权限分离**: host_permissions从permissions中分离
4. **API变更**: 多个API从同步改为异步

## 升级优势

### 1. 更好的性能
- Service Worker按需激活，减少内存占用
- 更快的启动速度
- 更好的资源管理

### 2. 增强的安全性
- 更严格的权限控制
- 内容安全策略(CSP)增强
- 减少攻击面

### 3. 未来兼容性
- Chrome将在2024年停止支持Manifest V2
- V3是Chrome扩展的未来标准
- 更好的长期维护性

### 4. 开发体验
- 更清晰的权限模型
- 更好的调试工具
- 更现代的API设计

## 迁移建议

1. **立即迁移**: 建议尽快迁移到V3版本
2. **测试验证**: 在测试环境充分验证所有功能
3. **用户通知**: 通知用户升级到新版本
4. **监控**: 密切监控V3版本的运行状况

## 总结

V3版本在保持所有原有功能的基础上，提供了：
- ✅ 更好的性能和资源利用
- ✅ 增强的安全性
- ✅ 更好的未来兼容性
- ✅ 完全相同的用户体验

建议立即升级到V3版本以获得更好的性能和长期支持。
