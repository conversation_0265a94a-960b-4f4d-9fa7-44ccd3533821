<!DOCTYPE html>
<html>
<head>
    <title>测试X-Bce-Request-Id</title>
</head>
<body>
    <h1>测试X-Bce-Request-Id生成</h1>
    <button id="testXHR">测试XMLHttpRequest</button>
    <button id="testFetch">测试Fetch API</button>
    <div id="results"></div>

    <script>
        const results = document.getElementById('results');

        document.getElementById('testXHR').addEventListener('click', () => {
            const xhr = new XMLHttpRequest();
            xhr.open('GET', 'https://httpbin.org/headers');
            xhr.onload = function() {
                const response = JSON.parse(xhr.responseText);
                const requestId = response.headers['X-Bce-Request-Id'];
                results.innerHTML += `<p>XHR Request ID: ${requestId}</p>`;
            };
            xhr.send();
        });

        document.getElementById('testFetch').addEventListener('click', () => {
            fetch('https://httpbin.org/headers')
                .then(response => response.json())
                .then(data => {
                    const requestId = data.headers['X-Bce-Request-Id'];
                    results.innerHTML += `<p>Fetch Request ID: ${requestId}</p>`;
                });
        });

        // 测试多次请求是否生成不同的ID
        function testMultipleRequests() {
            for (let i = 0; i < 3; i++) {
                setTimeout(() => {
                    fetch('https://httpbin.org/headers')
                        .then(response => response.json())
                        .then(data => {
                            const requestId = data.headers['X-Bce-Request-Id'];
                            results.innerHTML += `<p>Multiple Request ${i+1} ID: ${requestId}</p>`;
                        });
                }, i * 1000);
            }
        }

        // 自动测试
        setTimeout(testMultipleRequests, 2000);
    </script>
</body>
</html>
