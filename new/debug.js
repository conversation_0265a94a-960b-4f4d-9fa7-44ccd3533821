/**
 * 调试和测试脚本
 * 用于验证V3插件的基本功能
 */

// 测试chrome.storage.local是否工作
async function testStorage() {
    console.log('=== 测试Chrome Storage ===');
    
    try {
        // 设置测试数据
        await chrome.storage.local.set({
            'test_key': 'test_value',
            'bceCanarySwitch': 'on',
            'projectTag': 'test',
            'envTag': 'gray',
            'diverterTag': 'bcecanarytag'
        });
        console.log('✅ 存储设置成功');
        
        // 读取测试数据
        const result = await chrome.storage.local.get(['test_key', 'bceCanarySwitch', 'projectTag', 'envTag', 'diverterTag']);
        console.log('✅ 存储读取成功:', result);
        
        return true;
    } catch (error) {
        console.error('❌ 存储测试失败:', error);
        return false;
    }
}

// 测试declarativeNetRequest是否工作
async function testDeclarativeNetRequest() {
    console.log('=== 测试DeclarativeNetRequest ===');
    
    try {
        // 获取当前规则
        const rules = await chrome.declarativeNetRequest.getDynamicRules();
        console.log('✅ 当前动态规则:', rules);
        
        // 测试添加规则
        await chrome.declarativeNetRequest.updateDynamicRules({
            removeRuleIds: [999],
            addRules: [{
                id: 999,
                priority: 1,
                action: {
                    type: "modifyHeaders",
                    requestHeaders: [{
                        header: "X-Test-Header",
                        operation: "set",
                        value: "test-value"
                    }]
                },
                condition: {
                    urlFilter: "*",
                    resourceTypes: ["main_frame"]
                }
            }]
        });
        console.log('✅ 规则添加成功');
        
        // 清理测试规则
        await chrome.declarativeNetRequest.updateDynamicRules({
            removeRuleIds: [999]
        });
        console.log('✅ 规则清理成功');
        
        return true;
    } catch (error) {
        console.error('❌ DeclarativeNetRequest测试失败:', error);
        return false;
    }
}

// 测试tabs API是否工作
async function testTabsAPI() {
    console.log('=== 测试Tabs API ===');
    
    try {
        const tabs = await chrome.tabs.query({active: true, currentWindow: true});
        console.log('✅ 获取当前标签页成功:', tabs[0]?.url);
        return true;
    } catch (error) {
        console.error('❌ Tabs API测试失败:', error);
        return false;
    }
}

// 测试scripting API是否工作
async function testScriptingAPI() {
    console.log('=== 测试Scripting API ===');
    
    try {
        const tabs = await chrome.tabs.query({active: true, currentWindow: true});
        if (tabs[0]) {
            await chrome.scripting.executeScript({
                target: { tabId: tabs[0].id },
                func: () => {
                    console.log('✅ 脚本注入测试成功');
                    return 'test-result';
                }
            });
            console.log('✅ Scripting API测试成功');
            return true;
        }
    } catch (error) {
        console.error('❌ Scripting API测试失败:', error);
        return false;
    }
}

// 运行所有测试
async function runAllTests() {
    console.log('🚀 开始运行V3插件功能测试...');
    
    const results = {
        storage: await testStorage(),
        declarativeNetRequest: await testDeclarativeNetRequest(),
        tabsAPI: await testTabsAPI(),
        scriptingAPI: await testScriptingAPI()
    };
    
    console.log('📊 测试结果汇总:');
    console.table(results);
    
    const allPassed = Object.values(results).every(result => result === true);
    if (allPassed) {
        console.log('🎉 所有测试通过！插件功能正常');
    } else {
        console.log('⚠️ 部分测试失败，请检查相关功能');
    }
    
    return results;
}

// 如果在Service Worker中运行
if (typeof chrome !== 'undefined' && chrome.runtime) {
    // 延迟执行测试，确保Service Worker完全初始化
    setTimeout(runAllTests, 1000);
}

// 导出测试函数供外部调用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        testStorage,
        testDeclarativeNetRequest,
        testTabsAPI,
        testScriptingAPI,
        runAllTests
    };
}
