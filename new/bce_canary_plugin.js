/**
 * @file 百度云线上灰度测试的chrome插件实现，修改X-Bce-Request-Id和_xxx_version_:*******到sessionStorage中
 * <AUTHOR>
 */

// Service Worker 启动时初始化
chrome.runtime.onStartup.addListener(() => {
    console.log('BCE Canary Plugin Service Worker started');
});

chrome.runtime.onInstalled.addListener(() => {
    console.log('BCE Canary Plugin installed');
});

// 根据local storage里的参数、自动化设置request id
function generateRequestId() {
    function chunk() {
        let v = (~~(Math.random() * 0xffff)).toString(16);
        if (v.length < 4) {
            v += new Array(4 - v.length + 1).join('0');
        }
        return v;
    }

    const a = chunk();
    const b = chunk();
    const c = chunk();
    const d = chunk();
    const e = chunk();
    const f = chunk();
    const g = chunk();
    const h = chunk();

    return new Promise((resolve) => {
        chrome.storage.local.get(['projectTag', 'envTag', 'diverterTag', 'bceCanarySwitch'], (result) => {
            const { projectTag, envTag, diverterTag, bceCanarySwitch } = result;
            
            if (bceCanarySwitch === 'on') {
                resolve(a + b + '-' + c + '-' + envTag + '-' + projectTag + '-' + diverterTag);
            } else {
                resolve(a + b + '-' + c + '-' + d + '-' + e + '-' + f + g + h);
            }
        });
    });
}

// 监听并获取请求的cookie值
function getSession(resultsArray) {
    if (!resultsArray) {
        return;
    }
    chrome.storage.local.set({ 'canaryCookie': resultsArray[0] });
}

// 使用 declarativeNetRequest 替代 webRequest
// 动态更新规则来添加 X-Bce-Request-Id 头
async function updateRequestHeaders() {
    const requestId = await generateRequestId();
    
    // 移除旧规则
    await chrome.declarativeNetRequest.updateDynamicRules({
        removeRuleIds: [1]
    });
    
    // 添加新规则
    await chrome.declarativeNetRequest.updateDynamicRules({
        addRules: [{
            id: 1,
            priority: 1,
            action: {
                type: "modifyHeaders",
                requestHeaders: [{
                    header: "X-Bce-Request-Id",
                    operation: "set",
                    value: requestId
                }]
            },
            condition: {
                urlFilter: "*",
                resourceTypes: ["main_frame", "sub_frame", "xmlhttprequest"]
            }
        }]
    });
}

// 监听存储变化，当配置改变时更新规则
chrome.storage.onChanged.addListener((changes, namespace) => {
    if (namespace === 'local') {
        const relevantKeys = ['projectTag', 'envTag', 'diverterTag', 'bceCanarySwitch'];
        if (relevantKeys.some(key => key in changes)) {
            updateRequestHeaders();
        }
    }
});

// 因为sessionStorage是每个chrome tab页独特的，因此需根据tab id执行命令添加，实现整套方案真累死我了
function setFeVersionToSessionStroage(tabId) {
    chrome.storage.local.get(['feVersion', 'bceCanarySwitch'], (result) => {
        const { feVersion, bceCanarySwitch } = result;
        
        // 获取cookie
        chrome.scripting.executeScript({
            target: { tabId: tabId },
            func: () => document.cookie
        }).then((results) => {
            if (results && results[0]) {
                getSession([results[0].result]);
            }
        }).catch((error) => {
            console.log('Error getting cookie:', error);
        });

        var feVersionKey = [];
        var feVersionValue = [];
        
        // 从storage中获取fe version进行格式判断，其格式为_xxx_version_:*******
        if ((feVersion != null) && (feVersion.length > 3)) {
            var multiProjectList = feVersion.split(',');
            for (var i = 0; i < multiProjectList.length; i++) {
                var feVersionList = multiProjectList[i].split(':');
                if (feVersionList.length === 2) {
                    feVersionKey.push(feVersionList[0]);
                    feVersionValue.push(feVersionList[1]);
                } else {
                    return;
                }
            }
        } else {
            return;
        }

        // 只有fe version设置正确、开关为on状态，才向session storage设置fe版本号
        if (bceCanarySwitch === 'on') {
            if (tabId) {
                var i = 0;
                while (i < feVersionValue.length && feVersionValue[i] !== 'delete') {
                    const key = feVersionKey[i];
                    const value = feVersionValue[i];
                    chrome.scripting.executeScript({
                        target: { tabId: tabId },
                        func: (k, v) => window.sessionStorage.setItem(k, v),
                        args: [key, value]
                    }).catch((error) => {
                        console.log('Error setting sessionStorage:', error);
                    });
                    i++;
                }
            }
        }
        
        // 按钮为off 并且FeVersion有值的情况
        if (bceCanarySwitch === 'off') {
            if (tabId) {
                var i = 0;
                while (i < feVersionValue.length && feVersionValue[i] !== 'delete') {
                    const key = feVersionKey[i];
                    chrome.scripting.executeScript({
                        target: { tabId: tabId },
                        func: (k) => window.sessionStorage.setItem(k, ''),
                        args: [key]
                    }).catch((error) => {
                        console.log('Error clearing sessionStorage:', error);
                    });
                    i++;
                }
            }
        }

        // 如果之前有设置fe version，但开关不是on状态、或者现在fe version被置为了空、则向session storage中设置为空
        var i = 0;
        while (i < feVersionKey.length && feVersionValue[i] === 'delete') {
            const key = feVersionKey[i];
            chrome.scripting.executeScript({
                target: { tabId: tabId },
                func: (k) => window.sessionStorage.setItem(k, ''),
                args: [key]
            }).catch((error) => {
                console.log('Error deleting sessionStorage:', error);
            });
            i++;
        }
    });
}

// 监听tab激活信息
chrome.tabs.onActivated.addListener(function (activeInfo) {
    setFeVersionToSessionStroage(activeInfo.tabId);
});

// 监听tab创建
chrome.tabs.onCreated.addListener(function (tab) {
    setFeVersionToSessionStroage(tab.id);
});

// 监听tab更新
chrome.tabs.onUpdated.addListener(function (tabId, changeInfo, tab) {
    setFeVersionToSessionStroage(tabId);
    setFeVersionToSessionStroage(tab.id);
});

// 监听tab移动
chrome.tabs.onMoved.addListener(function (tabId, moveInfo) {
    setFeVersionToSessionStroage(tabId);
});

// 监听tab高亮
chrome.tabs.onHighlighted.addListener(function (highlightInfo) {
    var i = 0;
    var len = highlightInfo.tabIds.length;
    for (; i < len; i++) {
        if (highlightInfo.tabIds[i]) {
            setFeVersionToSessionStroage(highlightInfo.tabIds[i]);
        }
    }
});

// 监听tab从当前窗口分离
chrome.tabs.onDetached.addListener(function (tabId, detachInfo) {
    setFeVersionToSessionStroage(tabId);
});

// 监听tab加载到当前窗口
chrome.tabs.onAttached.addListener(function (tabId, attachInfo) {
    setFeVersionToSessionStroage(tabId);
});

// 初始化时更新请求头规则
updateRequestHeaders();
