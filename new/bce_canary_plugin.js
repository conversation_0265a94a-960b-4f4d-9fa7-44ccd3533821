/**
 * @file 百度云线上灰度测试的chrome插件实现，修改X-Bce-Request-Id和_xxx_version_:*******到sessionStorage中
 * <AUTHOR>
 */

// Service Worker 启动时初始化
chrome.runtime.onStartup.addListener(() => {
    console.log('BCE Canary Plugin Service Worker started');
});

chrome.runtime.onInstalled.addListener(() => {
    console.log('BCE Canary Plugin installed');
});

// 根据local storage里的参数、自动化设置request id
function generateRequestId() {
    function chunk() {
        let v = (~~(Math.random() * 0xffff)).toString(16);
        if (v.length < 4) {
            v += new Array(4 - v.length + 1).join('0');
        }
        return v;
    }

    const a = chunk();
    const b = chunk();
    const c = chunk();
    const d = chunk();
    const e = chunk();
    const f = chunk();
    const g = chunk();
    const h = chunk();

    return new Promise((resolve) => {
        chrome.storage.local.get(['projectTag', 'envTag', 'diverterTag', 'bceCanarySwitch'], (result) => {
            const { projectTag, envTag, diverterTag, bceCanarySwitch } = result;
            
            if (bceCanarySwitch === 'on') {
                resolve(a + b + '-' + c + '-' + envTag + '-' + projectTag + '-' + diverterTag);
            } else {
                resolve(a + b + '-' + c + '-' + d + '-' + e + '-' + f + g + h);
            }
        });
    });
}

// 监听并获取请求的cookie值
function getSession(resultsArray) {
    if (!resultsArray) {
        return;
    }
    chrome.storage.local.set({ 'canaryCookie': resultsArray[0] });
}

// V3版本中为了保持与V2版本一致的行为（每个请求都有不同的ID），
// 我们需要使用webRequest API的替代方案
// 由于declarativeNetRequest无法为每个请求生成不同的ID，我们使用content script注入的方式

// 存储当前配置
let currentConfig = {
    projectTag: '',
    envTag: '',
    diverterTag: '',
    bceCanarySwitch: 'off'
};

// 更新配置
async function updateConfig() {
    const result = await chrome.storage.local.get(['projectTag', 'envTag', 'diverterTag', 'bceCanarySwitch']);
    currentConfig = {
        projectTag: result.projectTag || '',
        envTag: result.envTag || '',
        diverterTag: result.diverterTag || '',
        bceCanarySwitch: result.bceCanarySwitch || 'off'
    };
}

// 为每个请求生成唯一的ID（同步版本，用于content script）
function generateRequestIdSync(config) {
    function chunk() {
        let v = (~~(Math.random() * 0xffff)).toString(16);
        if (v.length < 4) {
            v += new Array(4 - v.length + 1).join('0');
        }
        return v;
    }

    const a = chunk();
    const b = chunk();
    const c = chunk();
    const d = chunk();
    const e = chunk();
    const f = chunk();
    const g = chunk();
    const h = chunk();

    if (config.bceCanarySwitch === 'on') {
        return a + b + '-' + c + '-' + config.envTag + '-' + config.projectTag + '-' + config.diverterTag;
    } else {
        return a + b + '-' + c + '-' + d + '-' + e + '-' + f + g + h;
    }
}

// 注入content script来拦截请求
async function injectRequestInterceptor(tabId) {
    try {
        await chrome.scripting.executeScript({
            target: { tabId: tabId },
            func: (config) => {
                // 避免重复注入
                if (window.bceCanaryInjected) {
                    return;
                }
                window.bceCanaryInjected = true;

                // 生成请求ID的函数（在页面上下文中）
                function generateRequestIdSync(config) {
                    function chunk() {
                        let v = (~~(Math.random() * 0xffff)).toString(16);
                        if (v.length < 4) {
                            v += new Array(4 - v.length + 1).join('0');
                        }
                        return v;
                    }

                    const a = chunk();
                    const b = chunk();
                    const c = chunk();
                    const d = chunk();
                    const e = chunk();
                    const f = chunk();
                    const g = chunk();
                    const h = chunk();

                    if (config.bceCanarySwitch === 'on') {
                        return a + b + '-' + c + '-' + config.envTag + '-' + config.projectTag + '-' + config.diverterTag;
                    } else {
                        return a + b + '-' + c + '-' + d + '-' + e + '-' + f + g + h;
                    }
                }

                // 拦截XMLHttpRequest
                const originalXHROpen = XMLHttpRequest.prototype.open;
                const originalXHRSend = XMLHttpRequest.prototype.send;

                XMLHttpRequest.prototype.open = function(...args) {
                    this._bceRequestId = generateRequestIdSync(config);
                    return originalXHROpen.apply(this, args);
                };

                XMLHttpRequest.prototype.send = function(...args) {
                    if (this._bceRequestId) {
                        this.setRequestHeader('X-Bce-Request-Id', this._bceRequestId);
                    }
                    return originalXHRSend.apply(this, args);
                };

                // 拦截fetch API
                const originalFetch = window.fetch;
                window.fetch = function(input, init = {}) {
                    init.headers = init.headers || {};
                    if (init.headers instanceof Headers) {
                        init.headers.set('X-Bce-Request-Id', generateRequestIdSync(config));
                    } else {
                        init.headers['X-Bce-Request-Id'] = generateRequestIdSync(config);
                    }
                    return originalFetch.call(this, input, init);
                };
            },
            args: [currentConfig]
        });
    } catch (error) {
        console.log('Error injecting request interceptor:', error);
    }
}

// 监听存储变化，当配置改变时更新配置并重新注入所有tab
chrome.storage.onChanged.addListener(async (changes, namespace) => {
    if (namespace === 'local') {
        const relevantKeys = ['projectTag', 'envTag', 'diverterTag', 'bceCanarySwitch'];
        if (relevantKeys.some(key => key in changes)) {
            await updateConfig();
            // 重新注入所有活跃的tab
            try {
                const tabs = await chrome.tabs.query({});
                for (const tab of tabs) {
                    if (tab.url && !tab.url.startsWith('chrome://') && !tab.url.startsWith('chrome-extension://')) {
                        await injectRequestInterceptor(tab.id);
                    }
                }
            } catch (error) {
                console.log('Error re-injecting tabs:', error);
            }
        }
    }
});

// 因为sessionStorage是每个chrome tab页独特的，因此需根据tab id执行命令添加，实现整套方案真累死我了
function setFeVersionToSessionStroage(tabId) {
    chrome.storage.local.get(['feVersion', 'bceCanarySwitch'], (result) => {
        const { feVersion, bceCanarySwitch } = result;
        
        // 获取cookie
        chrome.scripting.executeScript({
            target: { tabId: tabId },
            func: () => document.cookie
        }).then((results) => {
            if (results && results[0]) {
                getSession([results[0].result]);
            }
        }).catch((error) => {
            console.log('Error getting cookie:', error);
        });

        var feVersionKey = [];
        var feVersionValue = [];
        
        // 从storage中获取fe version进行格式判断，其格式为_xxx_version_:*******
        if ((feVersion != null) && (feVersion.length > 3)) {
            var multiProjectList = feVersion.split(',');
            for (var i = 0; i < multiProjectList.length; i++) {
                var feVersionList = multiProjectList[i].split(':');
                if (feVersionList.length === 2) {
                    feVersionKey.push(feVersionList[0]);
                    feVersionValue.push(feVersionList[1]);
                } else {
                    return;
                }
            }
        } else {
            return;
        }

        // 只有fe version设置正确、开关为on状态，才向session storage设置fe版本号
        if (bceCanarySwitch === 'on') {
            if (tabId) {
                var i = 0;
                while (i < feVersionValue.length && feVersionValue[i] !== 'delete') {
                    const key = feVersionKey[i];
                    const value = feVersionValue[i];
                    chrome.scripting.executeScript({
                        target: { tabId: tabId },
                        func: (k, v) => window.sessionStorage.setItem(k, v),
                        args: [key, value]
                    }).catch((error) => {
                        console.log('Error setting sessionStorage:', error);
                    });
                    i++;
                }
            }
        }
        
        // 按钮为off 并且FeVersion有值的情况
        if (bceCanarySwitch === 'off') {
            if (tabId) {
                var i = 0;
                while (i < feVersionValue.length && feVersionValue[i] !== 'delete') {
                    const key = feVersionKey[i];
                    chrome.scripting.executeScript({
                        target: { tabId: tabId },
                        func: (k) => window.sessionStorage.setItem(k, ''),
                        args: [key]
                    }).catch((error) => {
                        console.log('Error clearing sessionStorage:', error);
                    });
                    i++;
                }
            }
        }

        // 如果之前有设置fe version，但开关不是on状态、或者现在fe version被置为了空、则向session storage中设置为空
        var i = 0;
        while (i < feVersionKey.length && feVersionValue[i] === 'delete') {
            const key = feVersionKey[i];
            chrome.scripting.executeScript({
                target: { tabId: tabId },
                func: (k) => window.sessionStorage.setItem(k, ''),
                args: [key]
            }).catch((error) => {
                console.log('Error deleting sessionStorage:', error);
            });
            i++;
        }
    });
}

// 处理tab的函数，同时处理sessionStorage和请求拦截器注入
async function handleTab(tabId) {
    setFeVersionToSessionStroage(tabId);
    try {
        const tab = await chrome.tabs.get(tabId);
        if (tab.url && !tab.url.startsWith('chrome://') && !tab.url.startsWith('chrome-extension://')) {
            await injectRequestInterceptor(tabId);
        }
    } catch (error) {
        console.log('Error handling tab:', error);
    }
}

// 监听tab激活信息
chrome.tabs.onActivated.addListener(function (activeInfo) {
    handleTab(activeInfo.tabId);
});

// 监听tab创建
chrome.tabs.onCreated.addListener(function (tab) {
    handleTab(tab.id);
});

// 监听tab更新
chrome.tabs.onUpdated.addListener(function (tabId, changeInfo, tab) {
    // 只在页面完成加载时注入，避免重复注入
    if (changeInfo.status === 'complete') {
        handleTab(tabId);
    } else {
        setFeVersionToSessionStroage(tabId);
    }
    setFeVersionToSessionStroage(tab.id);
});

// 监听tab移动
chrome.tabs.onMoved.addListener(function (tabId, moveInfo) {
    setFeVersionToSessionStroage(tabId);
});

// 监听tab高亮
chrome.tabs.onHighlighted.addListener(function (highlightInfo) {
    var i = 0;
    var len = highlightInfo.tabIds.length;
    for (; i < len; i++) {
        if (highlightInfo.tabIds[i]) {
            handleTab(highlightInfo.tabIds[i]);
        }
    }
});

// 监听tab从当前窗口分离
chrome.tabs.onDetached.addListener(function (tabId, detachInfo) {
    setFeVersionToSessionStroage(tabId);
});

// 监听tab加载到当前窗口
chrome.tabs.onAttached.addListener(function (tabId, attachInfo) {
    handleTab(tabId);
});

// 初始化
async function initialize() {
    await updateConfig();

    // 为所有现有的tab注入请求拦截器
    try {
        const tabs = await chrome.tabs.query({});
        for (const tab of tabs) {
            if (tab.url && !tab.url.startsWith('chrome://') && !tab.url.startsWith('chrome-extension://')) {
                await injectRequestInterceptor(tab.id);
            }
        }
    } catch (error) {
        console.log('Error initializing tabs:', error);
    }
}

// 启动初始化
initialize();
