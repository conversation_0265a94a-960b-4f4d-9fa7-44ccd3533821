<!DOCTYPE html>
<html>
<head>
    <title>BCE Canary Plugin V3 Test</title>
    <meta charset="utf-8">
</head>
<body>
    <h1>百度云灰度测试插件 V3 版本测试页面</h1>
    
    <h2>功能测试</h2>
    <div>
        <h3>1. 灰度分流功能</h3>
        <p>请打开插件popup，设置项目标签、环境标签、分流标签，然后开启开关</p>
        <button onclick="checkRequestId()">检查X-Bce-Request-Id</button>
        <div id="requestIdResult"></div>
    </div>
    
    <div>
        <h3>2. FE版本设置功能</h3>
        <p>请在插件中设置FE版本，然后检查sessionStorage</p>
        <button onclick="checkSessionStorage()">检查SessionStorage</button>
        <div id="sessionStorageResult"></div>
    </div>
    
    <div>
        <h3>3. 存储功能测试</h3>
        <button onclick="checkStorage()">检查Chrome Storage</button>
        <div id="storageResult"></div>
    </div>

    <script>
        function checkRequestId() {
            // 发送一个测试请求来检查X-Bce-Request-Id是否被添加
            fetch('/test', {
                method: 'GET'
            }).then(response => {
                document.getElementById('requestIdResult').innerHTML = 
                    '<p>请求已发送，请检查开发者工具的Network面板查看X-Bce-Request-Id头</p>';
            }).catch(error => {
                document.getElementById('requestIdResult').innerHTML = 
                    '<p>请求发送完成（预期会失败），请检查开发者工具的Network面板查看X-Bce-Request-Id头</p>';
            });
        }
        
        function checkSessionStorage() {
            let result = '<h4>SessionStorage内容:</h4><ul>';
            for (let i = 0; i < sessionStorage.length; i++) {
                let key = sessionStorage.key(i);
                let value = sessionStorage.getItem(key);
                result += `<li>${key}: ${value}</li>`;
            }
            result += '</ul>';
            document.getElementById('sessionStorageResult').innerHTML = result;
        }
        
        function checkStorage() {
            if (chrome && chrome.storage) {
                chrome.storage.local.get(null, (items) => {
                    let result = '<h4>Chrome Storage内容:</h4><ul>';
                    for (let key in items) {
                        result += `<li>${key}: ${items[key]}</li>`;
                    }
                    result += '</ul>';
                    document.getElementById('storageResult').innerHTML = result;
                });
            } else {
                document.getElementById('storageResult').innerHTML = 
                    '<p>Chrome Storage API不可用，请在Chrome扩展环境中测试</p>';
            }
        }
    </script>
</body>
</html>
