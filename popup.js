/**
 * @file 百度云线上灰度测试的chrome插件弹出的处理json，将所有页面的数据、设置到localStorage中
 * <AUTHOR>
 */
// 设置wiki打开
document.getElementById('helpWiki').onclick = function () {
    parent.open('http://wiki.baidu.com/pages/viewpage.action?pageId=1002931815');
};

// 访问amis,查看回放结果地址
document.getElementById("replayResultUrl").onclick = function () {
    var objS = document.getElementById("replayName");
    if (objS.selectedIndex != 0) {
        var project = objS.options[objS.selectedIndex].text;
    } else {
        var project = window.localStorage.getItem("lastReplayProject");
    }
    var objRegion = document.getElementById("replayRegion");
    if (objRegion.selectedIndex != 0) {
        var region = objRegion.options[objRegion.selectedIndex].text;
    } else {
        var region = window.localStorage.getItem("lastReplayRegion");
    }
    var objData = document.getElementById("replayData");
    if (objData.selectedIndex != 0) {
        var data = objData.options[objData.selectedIndex].text;
    } else {
        var data = window.localStorage.getItem("lastReplayData");
    }
    parent.open("http://amis.baidu.com/group/canary/canary/drainage/drainage_playback?project=" + project + "&region=" + region + "&dataId=" + data + "&page=1");
};

// 访问amis,查看抓取流量进度地址
document.getElementById("drainageUrl").onclick = function() {
    var objS = document.getElementById("drainageName");
    if (objS.selectedIndex != 0) {
        var project = objS.options[objS.selectedIndex].text;
    } else {
        var project = window.localStorage.getItem("lastDrainageProject");
    }
    var objDrainageRegion = document.getElementById("drainageRegion");
    if (objDrainageRegion.selectedIndex != 0) {
        var region = objDrainageRegion.options[objDrainageRegion.selectedIndex].text;
    } else {
        var region = window.localStorage.getItem("lastDrainageRegion");
    }
    parent.open("http://amis.baidu.com/group/canary/canary/drainage/drainage_job?project=" + project + "&region=" + region);
};

// canary地址,实现引流回放等任务
var canaryEndpoint = "canary.baidu-int.com";

// 设置开关按钮
var toggleButton = document.getElementById('toggle-button');
toggleButton.onchange = function () {
    if (toggleButton.checked) {
        // 判断项目标签合法性，其长度必须为4
        var reg = /^[a-zA-Z0-9]{4}$/;
        if (! reg.test(document.getElementById('projectTag').value)) {
            toggleButton.checked = false;
            window.localStorage.setItem('bceCanarySwitch', 'off');

            alert('非法的项目标签，其必须是字母或数字组成、长度为4的字符串');
            document.getElementById('projectTag').focus();
            return;
        }

        // 判断环境标签合法性，其长度必须为4
        reg = /^[a-zA-Z0-9]{4}$/;
        if (! reg.test(document.getElementById('envTag').value)) {
            toggleButton.checked = false;
            window.localStorage.setItem('bceCanarySwitch', 'off');

            alert('非法的环境标签，其必须是字母或数字组成、长度为4的字符串');
            document.getElementById('envTag').focus();
            return;
        }

        // 判断分流标签合法性，其长度必须为12
        reg = /^[a-zA-Z0-9]{12}$/;
        if (! reg.test(document.getElementById('diverterTag').value)) {
            toggleButton.checked = false;
            window.localStorage.setItem('bceCanarySwitch', 'off');

            alert('非法的分流标签，其必须是字母和数字组成的、长度为12的字符串');
            document.getElementById('diverterTag').focus();
            return;
        }

//        // 判断fe版本是否合法
//        var feVersion = document.getElementById('feVersion').value;
//        if (feVersion.length !== 0) {
//            reg = /^_[a-zA-Z0-9]*_version_:[0-9.]*$/;
//            if (! reg.test(feVersion)) {
//                toggleButton.checked = false;
//                window.localStorage.setItem('bceCanarySwitch', 'off');
//
//                alert('该fe版本号不准确，格式必须为:_xxx_version_:1.1.1.1，其中xxx为项目标识');
//                document.getElementById('feVersion').focus();
//                return;
//            }
//        }

        window.localStorage.setItem('bceCanarySwitch', 'on');

        updateXBceRequestIdExample();

    } else {
        window.localStorage.setItem('bceCanarySwitch', 'off');
    }
};

// 从历史数据中开关状态
var bceCanarySwitch = window.localStorage.getItem('bceCanarySwitch');
if (bceCanarySwitch === 'on') {
    toggleButton.checked = true;
} else {
    toggleButton.checked = false;
}

// 每次点开插件、都更新一次数据
var httpRequest = new XMLHttpRequest();
httpRequest.open('GET', 'http://' + canaryEndpoint + ':8000/chrome_plugin/show_info', true);
httpRequest.send();
httpRequest.onreadystatechange = function () {
    if (httpRequest.readyState == 4 && httpRequest.status == 200) {
        //var resString = '{"CNAP": {"diverter_tag": "666666666666", "project_tag": "caca", "env": {"sandbox_gray": "0000", "online_gray": "0001"}}, "MongoDB": {"diverter_tag": "bcecanarytag", "project_tag": "mgdb", "env": {"sandbox_gray": "0000", "online_gray": "0001"}}, "RabbitMQ": {"diverter_tag": "bcecanarytag", "project_tag": "mq00", "env": {"sandbox_gray": "0000", "online_gray": "0001"}}}'
        /* 返回的数据格式为：
        {
            "CNAP": {
                "project_tag": "caca",
                "diverter_tag": "666666666666",
                "env": {
                    "sandbox_gray": "0000",
                    "online_gray": "0001"
                }
            },
            "RabbitMQ": {
                "project_tag": "mq00",
                "diverter_tag": "bcecanarytag",
                "env": {
                    "sandbox_gray": "0000",
                    "online_gray": "0001"
                }
            }
        }
        */
        var resString = httpRequest.responseText;

        // 解析结果为json
        var resJson = JSON.parse(resString);

        // 从local storage中获取之前存储的选中的项目id
        var projectTagSelectProject = window.localStorage.getItem('projectTagSelectProject');

        // 根据json，将项目列表填充到projectTagSelect中
        var projectTagSelect = document.getElementById('projectTagSelect');
        projectTagSelect.style.display = '';
        projectTagSelect.add(new Option('', ''))
        var projectId = 1;
        var selectProjectId = 0;
        for (var eachProject in resJson) {
            projectTagSelect.add(new Option(eachProject, eachProject))

            // 找到之前选择的project tag的id
            if (eachProject === projectTagSelectProject) {
                selectProjectId = projectId;
            }
            projectId = projectId + 1;
        }

        // 设置之前设置的项目为选中状态
        projectTagSelect.options[selectProjectId].selected = true;

        // 从local storage中获取之前存储的选中的环境id
        var envTagSelectEnv = window.localStorage.getItem('envTagSelectEnv');

        // 根据该项目的env配置，设置envTagSelect中的值
        if (selectProjectId != 0) {
            // 用服务端反馈的最新值更新local stoage中的历史数据
            window.localStorage.setItem('projectTag', resJson[projectTagSelectProject]['project_tag']);
            window.localStorage.setItem('diverterTag', resJson[projectTagSelectProject]['diverter_tag']);

            // 动态设置env的select
            var envTagSelect = document.getElementById('envTagSelect');
            envTagSelect.style.display = '';
            envTagSelect.options.length = 0;
            envTagSelect.add(new Option('', ''))
            var envId = 1;
            var selectEnvId = 0;
            for (var eachEnv in resJson[projectTagSelectProject]['env']) {
                envTagSelect.add(new Option(eachEnv, eachEnv))

                // 找到之前选择的env tag的id
                if (eachEnv === envTagSelectEnv) {
                    selectEnvId = envId;

                    if (selectEnvId != 0) {
                        window.localStorage.setItem('envTag', resJson[projectTagSelectProject]['env'][eachEnv]);
                    }
                }
                envId = envId + 1;
            }

            // 设置之前设置的环境为选中状态
            envTagSelect.options[selectEnvId].selected = true;
        }

        // 保存string格式的数据到local storage中
        window.localStorage.setItem('projectTagSelect', resString);

        // 从历史数据中恢复项目标签
        document.getElementById('projectTag').value = window.localStorage.getItem('projectTag');
        // 从历史数据中恢复环境标签
        document.getElementById('envTag').value = window.localStorage.getItem('envTag');
        // 从历史数据中恢复分流器标签
        document.getElementById('diverterTag').value = window.localStorage.getItem('diverterTag');
        // 更新示例显示
        updateXBceRequestIdExample();
    }
};

function updateXBceRequestIdExample() {
    document.getElementById('xBceRequestIdExample').value = 'd53be6f3-bb3f-'
                                                            + document.getElementById('envTag').value
                                                            + '-'
                                                            + document.getElementById('projectTag').value
                                                            + '-'
                                                            + document.getElementById('diverterTag').value;

    window.localStorage.setItem('projectTag', document.getElementById('projectTag').value);
    window.localStorage.setItem('envTag', document.getElementById('envTag').value);
    window.localStorage.setItem('diverterTag', document.getElementById('diverterTag').value);
}

document.getElementById('projectTagSelect').onchange = function() {
    var projectTagSelect = document.getElementById('projectTagSelect');

    // 当前选中的id为：projectTagSelect.selectedIndex
    // 当前的所有选项为：projectTagSelect.options，取值为：projectTagSelect.options[projectTagSelect.selectedIndex].value
    // 设置某项为选中状态：projectTagSelect.options[id].selected = true;

    var projectTagSelectProject = projectTagSelect.options[projectTagSelect.selectedIndex].value;
   
    // 保存选中的select到local storage中，用于后续恢复
    window.localStorage.setItem('projectTagSelectProject', projectTagSelectProject);

    // 从local stoage中取出string格式的信息、并转换为json
    var resString = window.localStorage.getItem('projectTagSelect');
    var resJson = JSON.parse(resString);

    if (projectTagSelect.selectedIndex == 0) {
        document.getElementById('projectTag').value = '';
        document.getElementById('diverterTag').value = '';
        document.getElementById('envTag').value = '';
        
        // 将该已设置的envTagSelect关闭显示，回到初始状态
        document.getElementById('envTagSelect').style.display = 'none';
    } else {
        // 根据该值，更新projectTag、diverterTag字段
        document.getElementById('projectTag').value = resJson[projectTagSelectProject]['project_tag'];
        document.getElementById('diverterTag').value = resJson[projectTagSelectProject]['diverter_tag'];

        // 根据该项目的env配置，设置envTagSelect中的值
        var envTagSelect = document.getElementById('envTagSelect');
        envTagSelect.style.display = '';
        envTagSelect.options.length = 0;
        envTagSelect.add(new Option('', ''))
        for (var eachEnv in resJson[projectTagSelectProject]['env']) {
            envTagSelect.add(new Option(eachEnv, eachEnv))
        }
    }

    // 将开关设置为关闭状态
    toggleButton.checked = false;
    window.localStorage.setItem('bceCanarySwitch', 'off');

    // 清除之前设置的环境标签
    document.getElementById('envTag').value = '';

    updateXBceRequestIdExample();
}

document.getElementById('envTagSelect').onchange = function() {
    var envTagSelect = document.getElementById('envTagSelect');

    var projectTagSelectProject = projectTagSelect.options[projectTagSelect.selectedIndex].value;
    var envTagSelectEnv = envTagSelect.options[envTagSelect.selectedIndex].value;

    // 保存选中的select到local storage中，用于后续恢复
    window.localStorage.setItem('envTagSelectEnv', envTagSelectEnv);

    // 从local stoage中取出string格式的信息、并转换为json
    var resString = window.localStorage.getItem('projectTagSelect');
    var resJson = JSON.parse(resString);

    // 更新envTag字段
    if (envTagSelect.selectedIndex == 0) {
        document.getElementById('envTag').value = '';
    } else {
        document.getElementById('envTag').value = resJson[projectTagSelectProject]['env'][envTagSelectEnv];
    }

    // 将开关设置为关闭状态
    toggleButton.checked = false;
    window.localStorage.setItem('bceCanarySwitch', 'off');

    updateXBceRequestIdExample();
}

// 从历史数据中恢复fe版本号
var currentFeVersion = window.localStorage.getItem('feVersion');
if (currentFeVersion != null) {
    var multiProjectList = [];
    multiProjectList = currentFeVersion.split(',');
    if (multiProjectList.length > 0) {
        var currentFeVersionList = [];
        currentFeVersionList = multiProjectList[0].split(':');
        if (currentFeVersionList.length === 2) {
            if (currentFeVersionList[1] === 'delete') {
                document.getElementById('feVersion').value = '';
            } else {
                document.getElementById('feVersion').value = currentFeVersion;
            }
        }
    }
}
// 从历史数据中恢复项目标签
document.getElementById('projectTag').value = window.localStorage.getItem('projectTag');
// 从历史数据中恢复环境标签
document.getElementById('envTag').value = window.localStorage.getItem('envTag');
// 从历史数据中恢复分流器标签
document.getElementById('diverterTag').value = window.localStorage.getItem('diverterTag');
// 更新示例显示
updateXBceRequestIdExample();

// 获得界面设置的fe版本号
document.getElementById('feVersion').onchange = function() {
    var feVersion = document.getElementById('feVersion').value;
    if (feVersion.length === 0) {
        alert('注意：fe版本号为空，将清空之前的fe版本设置、使用已发布上线的Console');
        var oldFeVersion = window.localStorage.getItem('feVersion');
        if ((oldFeVersion != null) && (oldFeVersion.length > 3)) {
            var multiProjectList = [];
            multiProjectList = oldFeVersion.split(',');
            // 清空feVersion缓存
            var i = 0;
            var FeVerList = [];
            while (i < multiProjectList.length) {
                var oldFeVersionList = [];
                oldFeVersionList = multiProjectList[i].split(':');
                if (oldFeVersionList.length === 2) {
                    FeVerList.push(oldFeVersionList[0] + ':delete');
                }
                i++;
            }
            window.localStorage.setItem('feVersion', FeVerList);
        }
    } else {
        window.localStorage.setItem('feVersion', document.getElementById('feVersion').value);
    }
};
// 更新项目标签
document.getElementById('projectTag').onchange = function () {
    // 将project、env的select的值、设置为空
    if (document.getElementById('projectTagSelect').style.display === '') {
        document.getElementById('projectTagSelect').options[0].selected = true;
    }
    if (document.getElementById('envTagSelect').style.display === '') {
        document.getElementById('envTagSelect').options[0].selected = true;
    }
    window.localStorage.setItem('projectTagSelectProject', '');
    window.localStorage.setItem('envTagSelectEnv', '');

    // 先判断项目标签合法性，其长度必须为4
    var reg = /^[a-zA-Z0-9]{4}$/;
    if (! reg.test(document.getElementById('projectTag').value)) {
        toggleButton.checked = false;
        window.localStorage.setItem('bceCanarySwitch', 'off');

        alert('非法的项目标签，其必须是字母或数字组成、长度为4的字符串');
        document.getElementById('projectTag').focus();
        return;
    }

    updateXBceRequestIdExample();
};

// 更新环境标签
document.getElementById('envTag').onchange = function () {
    // 将env select的值设置为空
    if (document.getElementById('envTagSelect').style.display === '') {
        document.getElementById('envTagSelect').options[0].selected = true;
    }
    window.localStorage.setItem('envTagSelectEnv', '');

    // 先判断环境标签合法性，其长度必须为4
    var reg = /^[a-zA-Z0-9]{4}$/;
    if (! reg.test(document.getElementById('envTag').value)) {
        toggleButton.checked = false;
        window.localStorage.setItem('bceCanarySwitch', 'off');

        alert('非法的环境标签，其必须是字母或数字组成、长度为4的字符串');
        document.getElementById('envTag').focus();
        return;
    }

    updateXBceRequestIdExample();
};

// 更新分流器tag
document.getElementById('diverterTag').onchange = function () {
    // 先判断分流标签合法性，其长度必须为12
    var reg = /^[a-zA-Z0-9]{12}$/;
    if (! reg.test(document.getElementById('diverterTag').value)) {
        toggleButton.checked = false;
        window.localStorage.setItem('bceCanarySwitch', 'off');

        alert('非法的分流标签，其必须是字母或数字组成、长度为12的字符串');
        document.getElementById('diverterTag').focus();
        return;
    }

    updateXBceRequestIdExample();
};


// 分流 引流 回放的tab
var tab_list = document.querySelector('.tab-list');
var lis = tab_list.querySelectorAll('li');
// 循环绑定点击事件
for (var i = 0; i < lis.length; i++) {
    lis[i].setAttribute('index', i);
    lis[i].onclick = function() {
        for (var j = 0; j < lis.length; j++) {
            lis[j].className = '';
        }
        // 留下现在需要用的
        this.className = 'drainage';
        // 下面显示内容模块
        var index = this.getAttribute('index');
        window.localStorage.setItem('selectedTab', index);
        console.log(index);
        // 去掉其他的 item，让这些隐藏起来
        // 只留下当前的 item，让它显示出来
        for (var i = 0; i < items.length; i++) {
            items[i].style.display = 'none';
        }
        items[index].style.display = 'block';
    };
}

// 点击选中tab栏，设置选中对象颜色状态变化
var ul = document.getElementsByTagName("ul")[0];
var li = document.getElementsByTagName("li");
// 默认加载插件时 显示分流tab选中设为蓝色
li[0].style.background = '#99CCFF';
ul.onclick = function(e) {
    for (var i = 0; i < li.length; i++) {
        li[i].style.background = '#FFFFFF';
    }
    var temp = e.srcElement;
    temp.style.background = '#99CCFF';
}

// 默认显示上次操作所在tab,若之前在分流tab页面，则再次打开插件也会显示分流tab页面
var items = document.querySelectorAll(".item");
for (var i = 0; i < items.length; i++) {
    items[i].style.display = 'none';
}

if (window.localStorage.getItem('selectedTab')) {
    items[window.localStorage.getItem('selectedTab')].style.display = 'block';
    // 点击选中tab栏，设置选中对象颜色状态变化
    var ul = document.getElementsByTagName("ul")[0];
    var li = document.getElementsByTagName("li");
    li[0].style.background = '#FFFFFF';
    for (var i = 0; i < li.length; i++) {
        li[i].style.background = '#FFFFFF';
    }
    li[window.localStorage.getItem('selectedTab')].style.background = '#99CCFF';
} else {
    items[0].style.display = 'block';
}

/**
* @Description: 获取项目列表,恢复上次操作选择的各个参数
* @Param: GET  amis/project/list
* @return:
* @Author: yangliqin01
*/
var projectRequest = new XMLHttpRequest();
projectRequest.open('GET', "http://" + canaryEndpoint + "/amis/project/list", true);
projectRequest.send();

// 获取数据后的处理程序,动态返回项目名称, 并写入下拉列表中
projectRequest.onreadystatechange = function() {
    if (projectRequest.readyState == 4 && projectRequest.status == 200) {
        var json = projectRequest.responseText;
        // 获取到json字符串，还需解析
        var resJson = JSON.parse(json);
        // 解析字符串---解析成object数组
        for (var i = 0; i < resJson.data.count; i++) {
            var node = document.createElement("option");
            var projectName = document.createTextNode(resJson.data.rows[i].project);
            node.appendChild(projectName);
            document.getElementById("drainageName").appendChild(node);

            var replayNode = document.createElement("option");
            var replayProjectName = document.createTextNode(resJson.data.rows[i].project);
            replayNode.value = replayProjectName;
            replayNode.appendChild(replayProjectName);
            document.getElementById("replayName").appendChild(replayNode);
        }
        // 从历史数据中恢复项目标签，避免用户切换插件页面后，参数信息丢失导致无法停止任务
        var selectedDrainageName = window.localStorage.getItem('selectedDrainageName');
        if (selectedDrainageName && selectedDrainageName != 0) {
            var objS = document.getElementById("drainageName");
            objS.options[selectedDrainageName].selected = true;
        }
        if (window.localStorage.getItem('taskName')) {
            document.getElementById('taskName').value = window.localStorage.getItem('taskName');
        }
        // 从历史数据中恢复回放功能 项目标签值，避免用户切换插件页面后，参数信息丢失导致无法停止任务
        var selectedReplayName = window.localStorage.getItem('selectedReplayName');
        if (selectedReplayName && selectedReplayName != 0) {
            var objS = document.getElementById("replayName");
            objS.options[selectedReplayName].selected = true;
        }
        var selectedAuthType = window.localStorage.getItem('selectedAuthType');
        if (selectedAuthType && selectedAuthType != 0) {
            var objS = document.getElementById("AuthType");
            objS.options[selectedAuthType].selected = true;
        }

        // 退出后恢复进度条
        if (window.localStorage.getItem('replayProcess')) {
            var myProgress = document.getElementById("myProgress");
            var mySpan = document.getElementById("mySpan");
            myProgress.value = window.localStorage.getItem('replayProcess');
            mySpan.innerText = window.localStorage.getItem('replayProcess') + "%";
        }

        // 退出插件后自动恢复各级下拉框默认值
        getDefaultOption("selectedDrainageRegion", "drainageRegionJson", "drainageRegion");
        getDefaultOption("selectedDrainageType", "drainageTypeJson", "drainageType");

        getDefaultOption("selectedReplayRegion", "replayRegionJson", "replayRegion");
        getDefaultOption("selectedReplayType", "replayTypeJson", "replayType");
        getDefaultOption("selectedReplayData", "replayDataJson", "replayData");
        // 进行回放进度检查，保证退出插件依然可以继续获取回放状态
        checkReplayProcess();
    }
};

// 为了解决退出插件后自动恢复各级下拉框默认值的问题。实现方案是通过本地存储历史请求返回的json，重建各级下拉框，
// 再从缓存中读取历史的下拉框列表值，实现恢复默认的下拉框列表值，引流结束任务后，清理缓存值。
// 好处是无需重新向后端发请求，即可恢复默认值

function getDefaultOption(selectedOption, itemJson, itemId) {
    var selectedItemOption = window.localStorage.getItem(selectedOption);
    if (selectedItemOption) {
        var objS = document.getElementById(itemId);

        // 获取缓存的json用于重建option
        var itemJsonInfo = window.localStorage.getItem(itemJson);
        if (itemJsonInfo) {
            getOptionFromJson(itemJsonInfo, itemId);
        }
        objS.options[selectedItemOption].selected = true;
    }
}

/**
* @Description: 通过选择项目下拉菜单，获取地域列表；当项目变更，对应的地域不变的时候，通过项目下拉框触发请求，生成新的抓取类型和抓取任务名
* @Param: GET  amis/project/list_region?project=${project}
* @return:
* @Author: yangliqin01
*/
document.getElementById('drainageName').onchange = function() {
    // 首先删除原有的下拉框选项，重新触发请求，获取地域下拉框
    document.getElementById("drainageRegion").options.length = 1;
    getDrainageRegion();

};

function getDrainageRegion() {
    var objDrainageName = document.getElementById("drainageName");
    var name = objDrainageName.options[objDrainageName.selectedIndex].text;
    var url = "http://" + canaryEndpoint + "/amis/project/list_region?project=" + name;

    var locationRequest = new XMLHttpRequest();
    locationRequest.open('GET', url, true);
    locationRequest.send();
    // 选中项目，动态返回对应的地域名称, 并写入地域下拉列表中
    locationRequest.onreadystatechange = function() { //固定写法
        if (locationRequest.readyState === 4) {
            if (locationRequest.status === 200) {
                var json = locationRequest.responseText;
                // 获取到json字符串，还需解析
                getOptionFromJson(json, 'drainageRegion');
                var objRegion = document.getElementById('drainageRegion');
                var selectedOptionRegion = window.localStorage.getItem('selectedDrainageRegion');
                if (selectedOptionRegion) {
                    objRegion.options[selectedOptionRegion].selected = true;
                }
                // 缓存json用于重建option
                window.localStorage.setItem('drainageRegionJson', json);
            } else {
                alert('获取地域列表异常，请稍后重试！');
            }
        }
    }
}

// 根据获取到的json信息，重建下拉框并设置对应option的值
function getOptionFromJson(json, elementId) {
    var info = JSON.parse(json);
    for (var i = 0; i < Object.keys(info.data.options).length; i++) {
        var selectedLocation = info.data.options[i];
        var locationOption = document.createElement("option");
        var projectName = document.createTextNode(info.data.options[i].value);
        locationOption.appendChild(projectName);
        document.getElementById(elementId).appendChild(locationOption);
    }
}

/**
* @Description: 通过地域下拉菜单，获取抓取类型,并且生成抓取任务名
* @Param: GET   /amis/cluster/list_env_by_upstream?project=${project}&region=${region}
* @return:
* @Author: yangliqin01
*/
document.getElementById('drainageRegion').onchange = function() {
    document.getElementById("drainageType").options.length=1;
    getDrainageType();
    getDrainageTask();
};

function getDrainageType() {
    var objDrainageName = document.getElementById("drainageName");
    var project = objDrainageName.options[objDrainageName.selectedIndex].text;
    window.localStorage.setItem("lastDrainageProject", project);

    var objRegion = document.getElementById("drainageRegion");
    var region = objRegion.options[objRegion.selectedIndex].text;
    window.localStorage.setItem("lastDrainageRegion", region);
    var url = "http://" + canaryEndpoint + "/amis/cluster/list_env_by_upstream?project=" + project + "&region=" + region;

    var drainageTypeRequest = new XMLHttpRequest();
    drainageTypeRequest.open('GET', url, true);
    drainageTypeRequest.send();
    // 选中项目，动态返回对应的抓取类型, 并写入抓取类型下拉列表中
    drainageTypeRequest.onreadystatechange = function() { //固定写法
        if (drainageTypeRequest.readyState === 4) {
            if (drainageTypeRequest.status === 200) {
                var json = drainageTypeRequest.responseText;
                getOptionFromJson(json, 'drainageType');
                var objType = document.getElementById('drainageType');
                var selectedOptionType = window.localStorage.getItem('selectedDrainageType');
                if (selectedOptionType) {
                    objType.options[selectedOptionType].selected = true;
                }
                // 缓存json用于重建option
                window.localStorage.setItem('drainageTypeJson', json);
            } else {
                alert('获取抓取类型,生成抓取任务名异常，请稍后重试！');
            }
        }
    }
}

// 生成抓取流量任务名称，传给后端
function getDrainageTask(){
var objDrainageName = document.getElementById("drainageName");
    var project = objDrainageName.options[objDrainageName.selectedIndex].text;
    var objRegion= document.getElementById("drainageRegion");
    var region = objRegion.options[objRegion.selectedIndex].text;

    // 自动生成抓取任务名，项目+地域+时间戳
    //var date = new Date(+new Date()+8*3600*1000).toISOString().replace(/T/g,'').replace(/\.[\d]{3}Z/,'').replace(/\s/ig,'');
    var date = getTimeStr();
    var jobName = project + "_" + region + "_" + date;
    document.getElementById("taskName").value = jobName;
}

function getTimeStr() {
    var date = new Date();
    var year = date.getFullYear();
    var month = date.getMonth() + 1;
    var day = date.getDate();
    var hours = date.getHours();
    var minutes = date.getMinutes();
    if (month.length == 1) {
        month = "0" + month;
    }
    if (day.length == 1) {
        day = "0" + day;
    }
    if (hours.length == 1) {
        hours = "0" + hours;
    }
    if (minutes.length == 1) {
        minutes = "0" + minutes;
    }
    var date = year + "" + month + "" + day + "" + hours + "" + minutes;
    return date;
}

/**
* @Description:点击开始按钮 ，触发开始引流
* @Param: GET   plugin/drainage/grab?action=start
* @return:
* @Author: yangliqin01
*/
document.getElementById('drainage_start').onclick = function() {
    // 清除点击结束引流操作记录
    if(window.localStorage.getItem('drainageStopClick')){
        window.localStorage.removeItem('drainageStopClick');
    }
    // 用户进行了开始引流动作的记录
    window.localStorage.setItem('drainageStartClick', "true");
    var objDrainageName = document.getElementById("drainageName");
    var project = objDrainageName.options[objDrainageName.selectedIndex].text;
    window.localStorage.setItem('selectedDrainageName', objDrainageName.selectedIndex);
    window.localStorage.setItem("lastDrainageProject", project);

    var objDrainageRegion = document.getElementById("drainageRegion");
    var region = objDrainageRegion.options[objDrainageRegion.selectedIndex].text;
    window.localStorage.setItem('selectedDrainageRegion', objDrainageRegion.selectedIndex);
    window.localStorage.setItem("lastDrainageRegion", region);

    var objDrainageType = document.getElementById("drainageType");
    var requestType = objDrainageType.options[objDrainageType.selectedIndex].text;
    window.localStorage.setItem('selectedDrainageType', objDrainageType.selectedIndex);
    // 保存本次创建引流任务的参数，以string格式的数据存到local storage
    var jobName = document.getElementById("taskName").value;
    // 抓取任务名称
    window.localStorage.setItem('taskName', jobName);

    // 对参数的异常判断，若为空提示用户选择对应参数
    var paramCheck = drainageParamCheck();
    if (paramCheck == true) {
        var url = "http://" + canaryEndpoint + "/plugin/drainage/grab?action=start&project=" + project + "&region=" + region + "&grabType=" + requestType + "&jobName=" + jobName;
        var drainageRequest = new XMLHttpRequest();
        drainageRequest.open('GET', url, true);
        drainageRequest.send();
        drainageRequest.onreadystatechange = function() {
            var json = drainageRequest.responseText;
            // 获取到json字符串，还需解析
            var resJson = JSON.parse(json);
            // 解析字符串---解析成object数组
            if (drainageRequest.readyState === 4) {
                if (drainageRequest.status === 200) {
                    if (resJson.msg === 'succ') {
                        alert('抓取任务创建成功！');
                    } else {
                        alert(resJson.msg);
                    }
                } else {
                    alert('获取抓取类型,生成抓取任务名异常，请稍后重试！');
                }
            }
        }
        // 开始引流按钮变灰不可用,结束按钮可用
        document.getElementById("drainage_start").setAttribute("disabled", true);
        document.getElementById("drainage_stop").removeAttribute("disabled");
    } else {
        return;
    }

};
drainageStartClick();

// 抓取过程中退出插件后 开始抓取置灰,结束抓取可用，重新进入插件后默认恢复
function drainageStartClick() {
    // 没有开始的时候 结束按钮置灰
    document.getElementById("drainage_stop").setAttribute("disabled", true);
    // 此处需加上空判断，保证首次加载插件重复点击开始，开始按钮可用，保证点击结束后由于清空缓存无法触发
    if (window.localStorage.getItem('selectedDrainageName') != 0 && window.localStorage.getItem('selectedDrainageRegion') != 0 && window.localStorage.getItem('selectedDrainageType') != 0) {
        if (window.localStorage.getItem('drainageStartClick') == "true") {
            document.getElementById("drainage_start").setAttribute("disabled", true);
            document.getElementById("drainage_stop").removeAttribute("disabled");
        }
    }
}

// 结束抓取后退出插件后 结束抓取置灰,开始抓取可用，重新进入插件后默认恢复
function drainageStopClick(){
    if(window.localStorage.getItem('drainageStopClick') == "true"){
        document.getElementById("drainage_stop").setAttribute("disabled", true);
        document.getElementById("drainage_start").removeAttribute("disabled");
    }
}

// 点击开始、结束按钮,对参数的异常判断，若为空提示用户选择对应参数
function drainageParamCheck() {
    if (window.localStorage.getItem('selectedDrainageName') == null || window.localStorage.getItem('selectedDrainageName') == 0) {
        alert("项目参数为空，请在列表选择项目！");
        return false;
    }
    if (window.localStorage.getItem('selectedDrainageRegion') == 0) {
        alert("地域参数为空，请在列表选择地域！");
        return false;
    }
    if (window.localStorage.getItem('selectedDrainageType') == 0) {
        alert("抓取类型为空，请在列表选择抓取类型！");
        return false;
    }
    return true;
}

/**
* @Description: 点击结束引流按钮 ，触发引流结束
* @Param: GET   plugin/drainage/grab?action=stop
* @return:
* @Author: yangliqin01
*/
document.getElementById('drainage_stop').onclick = function() {
    // 清除点击开始引流操作记录
    if(window.localStorage.getItem('drainageStartClick')){
        window.localStorage.removeItem('drainageStartClick');
    }
    // 用户进行了结束引流动作的记录
    window.localStorage.setItem('drainageStopClick', "true");
    // 项目名称
    var objDrainageName = document.getElementById("drainageName");
    var project = objDrainageName.options[objDrainageName.selectedIndex].text;
    window.localStorage.setItem("lastDrainageProject", project);
    // 地域
    var objDrainageRegion = document.getElementById("drainageRegion");
    var region = objDrainageRegion.options[objDrainageRegion.selectedIndex].text;
    window.localStorage.setItem("lastDrainageRegion", region);
    // 类型
    var objDrainageType = document.getElementById("drainageType");
    // 抓取任务名称
    var jobName = document.getElementById("taskName").value;
    // 对参数的异常判断，若为空提示用户选择对应参数
    var paramCheck = drainageParamCheck();
    if (paramCheck == true) {
        var url = "http://" + canaryEndpoint + "/plugin/drainage/grab?action=finish&project=" + project + "&region=" + region + "&jobName=" + jobName;
        var drainageStopRequest = new XMLHttpRequest();
        drainageStopRequest.open('GET', url, true);
        drainageStopRequest.send();
        drainageStopRequest.onreadystatechange = function() { //固定写法
            var json = drainageStopRequest.responseText; //获取到json字符串，还需解析
            var resJson = JSON.parse(json); //解析字符串---解析成object数组
            if (drainageStopRequest.readyState === 4) {
                if (drainageStopRequest.status === 200) {
                    if (resJson.msg === 'succ') {
                        alert('抓取任务已停止！');
                    } else {
                        alert(resJson.msg);
                    }
                } else {
                    alert(resJson.msg);
                }
            }
        }
        // 清空引流参数，清空缓存，禁止用户重复提交同一个引流任务
        objDrainageName.options[0].selected = true;
        objDrainageRegion.options[0].selected = true;
        objDrainageType.options[0].selected = true;
        document.getElementById("taskName").value = "";
        window.localStorage.removeItem('selectedDrainageName');
        window.localStorage.removeItem('selectedDrainageRegion');
        window.localStorage.removeItem('selectedDrainageType');
        window.localStorage.removeItem('taskName');

        window.localStorage.removeItem('drainageRegionJson');
        window.localStorage.removeItem('drainageTypeJson');
        // 结束引流按钮变灰不可用,开始按钮可用
        document.getElementById("drainage_stop").setAttribute("disabled", true);
        document.getElementById("drainage_start").removeAttribute("disabled");
    } else {
        return;
    }
};
drainageStopClick();

/**
* @Description: 通过选择项目下拉菜单，获取地域列表；当项目变更，对应的地域不变的时候，通过项目下拉框触发请求，生成新的抓取类型和抓取任务名
* @Param: GET  amis/project/list_region?project=${project}
* @return:
* @Author: yangliqin01
*/
document.getElementById('replayName').onchange = function() {
    // 首先删除原有的下拉框选项，重新触发请求，获取地域下拉框
    document.getElementById("replayRegion").options.length=1;
    getReplayRegion();
};

function getReplayRegion() {
    var objReplayName = document.getElementById("replayName");
    var name = objReplayName.options[objReplayName.selectedIndex].text;
    var url = "http://" + canaryEndpoint + "/amis/project/list_region?project=" + name;

    var locationRequest = new XMLHttpRequest();
    locationRequest.open('GET', url, true);
    locationRequest.send();
    // 选中项目，动态返回对应的地域名称, 并写入地域下拉列表中
    locationRequest.onreadystatechange = function() {
        if (locationRequest.readyState === 4) {
            if (locationRequest.status === 200) {
                var json = locationRequest.responseText;
                // 获取到json字符串，还需解析
                getOptionFromJson(json, 'replayRegion');
                var objRegion = document.getElementById('replayRegion');
                var selectedOptionRegion = window.localStorage.getItem('selectedReplayRegion');
                if (selectedOptionRegion) {
                    objRegion.options[selectedOptionRegion].selected = true;
                }
                // 缓存json用于重建option
                window.localStorage.setItem('replayRegionJson', json);
            } else {
                alert('获取地域列表异常，请稍后重试！');
            }
        }
    }
}

/**
* @Description: 通过地域下拉菜单，获取抓取类型,并且生成抓取任务名
* @Param: GET   /amis/cluster/list_env_by_upstream?project=${project}&region=${region}
* @return:
* @Author: yangliqin01
*/
document.getElementById('replayRegion').onchange = function() {
    document.getElementById("replayType").options.length=1;
    getReplayType();
    document.getElementById('replayData').options.length=1;
    getReplayData();
};


function getReplayType() {
    var objReplayName = document.getElementById("replayName");
    var project = objReplayName.options[objReplayName.selectedIndex].text;

    var objRegion = document.getElementById("replayRegion");
    var region = objRegion.options[objRegion.selectedIndex].text;

    var url = "http://" + canaryEndpoint + "/amis/cluster/list_env_by_upstream?project="
    + project + "&region=" + region;

    var replayTypeRequest = new XMLHttpRequest();
    replayTypeRequest.open('GET', url, true);
    replayTypeRequest.send();

    // 选中项目，动态返回对应的抓取类型, 并写入抓取类型下拉列表中
    replayTypeRequest.onreadystatechange = function() {
        if (replayTypeRequest.readyState === 4) {
            if (replayTypeRequest.status === 200) {
                var json = replayTypeRequest.responseText;
                // 获取到json字符串，还需解析
                getOptionFromJson(json, 'replayType');

                var objType = document.getElementById('replayType');
                var selectedOptionType = window.localStorage.getItem('selectedReplayType');
                if (selectedOptionType) {
                    objType.options[selectedOptionType].selected = true;
                }
                // 缓存json用于重建option
                window.localStorage.setItem('replayTypeJson', json);
            } else {
                alert('获取抓取类型,生成抓取任务名异常，请稍后重试！');
            }
        }
    }
}

// 保存上一次的回放数据集
document.getElementById('replayData').onchange = function() {
    var objReplayData = document.getElementById("replayData");
    var replayData = objReplayData.options[objReplayData.selectedIndex].text;
    window.localStorage.setItem("lastReplayData", replayData);
};

/**
* @Description: 获取回放数据集
* @Param: GET   amis/drainage/replay_data/list_dataids
* @return:
* @Author: yangliqin01
*/
function getReplayData() {
    var objReplayName = document.getElementById("replayName");
    var project = objReplayName.options[objReplayName.selectedIndex].text;
    window.localStorage.setItem("lastReplayProject", project);

    var objRegion = document.getElementById("replayRegion");
    var region = objRegion.options[objRegion.selectedIndex].text;
    window.localStorage.setItem("lastReplayRegion", region);

    var url = "http://" + canaryEndpoint + "/amis/drainage/replay_data/list_dataids?project=" + project + "&region=" + region;

    var replayDataRequest = new XMLHttpRequest();
    replayDataRequest.open('GET', url, true);
    replayDataRequest.send();

    replayDataRequest.onreadystatechange = function() { //固定写法
        if (replayDataRequest.readyState === 4) {
            if (replayDataRequest.status === 200) {
                var json = replayDataRequest.responseText; // 获取到json字符串，还需解析
                getOptionFromJson(json, 'replayData');
                var objType = document.getElementById('replayData');
                var selectedOptionType = window.localStorage.getItem('selectedReplayData');
                if (selectedOptionType) {
                    objType.options[selectedOptionType].selected = true;
                }
                // 缓存json用于重建option
                window.localStorage.setItem('replayDataJson', json);
            } else {
                alert('获取回放数据集异常，请稍后重试！');
            }
        }
    }
}

/**
* @Description: 点击开始按钮 ，触发开始回放
* @Param: GET   plugin/drainage/replay?action=start
* @return:
* @Author: yangliqin01
*/
document.getElementById('replay_start').onclick = function() {
    // 清除上一次回放结束的记录，重要
    if (window.localStorage.getItem('replayFinish') == "true") {
        window.localStorage.removeItem('replayFinish');
    }
    // 清除点击暂停回放、继续回放操作记录
    if(window.localStorage.getItem('replayStopClick')){
        window.localStorage.removeItem('replayStopClick');
    }
    if(window.localStorage.getItem('replayResumeClick')){
        window.localStorage.removeItem('replayResumeClick');
    }
    // 用户进行了开始回放动作的记录
    window.localStorage.setItem('replayStartClick', "true");

    // 用户进行了开始回放,从缓存中获取请求cookie值,用于回放鉴权
    var canaryCookie = 0;
    if(window.localStorage.getItem('canaryCookie')){
        canaryCookie = window.localStorage.getItem('canaryCookie');
    }
    var objReplayName = document.getElementById("replayName");
    var project = objReplayName.options[objReplayName.selectedIndex].text; //项目名称
    window.localStorage.setItem('selectedReplayName', objReplayName.selectedIndex);
    window.localStorage.setItem("lastReplayProject", project);

    var objReplayRegion = document.getElementById("replayRegion");
    var region = objReplayRegion.options[objReplayRegion.selectedIndex].text; //地域
    window.localStorage.setItem('selectedReplayRegion', objReplayRegion.selectedIndex);
    window.localStorage.setItem("lastReplayRegion", region);

    var objReplayType = document.getElementById("replayType");
    var replayEnv = objReplayType.options[objReplayType.selectedIndex].text; //请求类型
    window.localStorage.setItem('selectedReplayType', objReplayType.selectedIndex);

    var objAuthType = document.getElementById("AuthType");
    var authType = objAuthType.options[objAuthType.selectedIndex].value; // 鉴权方式
    window.localStorage.setItem('selectedAuthType', objAuthType.selectedIndex);

    // 保存本次创建的回放数据集，以string格式的数据存到local storage
    var objReplayData = document.getElementById("replayData");
    var replayData = objReplayData.options[objReplayData.selectedIndex].text;
    window.localStorage.setItem('selectedReplayData', objReplayData.selectedIndex);
    window.localStorage.setItem("lastReplayData", replayData);
    if (window.localStorage.getItem('replayTaskId')) {
        // 回放任务名称，传给后端用
        var jobName = window.localStorage.getItem('replayTaskId');
    }
    // 对参数的异常判断，若为空提示用户选择对应参数
    var paramCheck = replayParamCheck();
    if (paramCheck == true) {
        // 设置正在回放中
        window.localStorage.setItem('replayStatus', "true");
        var url = "http://" + canaryEndpoint + "/plugin/drainage/replay?action=start&project=" + project + "&region=" + region + "&dataId=" + replayData + "&replayEnv=" + replayEnv + "&authType=" +authType;
        var replayStartRequest = new XMLHttpRequest();
        replayStartRequest.open('GET', url, true);
        if(authType ==='cookie') {
            replayStartRequest.setRequestHeader('canaryCookie',canaryCookie);
        }
        replayStartRequest.send();
        replayStartRequest.onreadystatechange = function() {
            var json = replayStartRequest.responseText;
            var resJson = JSON.parse(json);
            if (replayStartRequest.readyState === 4) {
                if (replayStartRequest.status === 200) {
                    if (resJson.msg === 'succ') {
                        // 缓存后端返回的回放数据集，传给后端，用于暂停回放、继续回放的接口参数
                        window.localStorage.setItem('replayTaskId', resJson.msg);
                    } else {
                        alert(resJson.msg);
                    }
                } else {
                    alert('创建回放任务异常，请稍后重试！');
                }
            }
        }

        // sleep 后，显示进度条,等待上一个请求结束
        sleep(750).then(() => {
            showReplayProgress(project, region, jobName);
        })
        // 开始回放按钮变灰不可用,暂停回放按钮可用
        document.getElementById("replay_start").setAttribute("disabled", true);
        document.getElementById("replay_stop").removeAttribute("disabled");
        document.getElementById("replay_restart").setAttribute("disabled", true);
    } else {
        return;
    }
};
replayStartClick();

/**
* @Description: 请求后端获取回放进度，前端实时显示进度条
* @Param: GET   /plugin/drainage/replay?action=get_process
* @return:
* @Author: yangliqin01
*/
function showReplayProgress(project, region, jobName) {
    // 获取并显示回放进度
    document.getElementById("myProgress").style.display = "inline";
    document.getElementById("mySpan").style.display = "inline";

    var myProgress = document.getElementById("myProgress");
    var mySpan = document.getElementById("mySpan");

    if (window.localStorage.getItem('replayProcess') == null) {
        window.localStorage.setItem('replayProcess', 1);
    }

    myProgress.value = window.localStorage.getItem('replayProcess');

    var ID = setInterval(function() {
        if (parseInt(window.localStorage.getItem('replayProcess')) < 100) {
            // 缓存后端返回的回放数据集，传给后端
            if (window.localStorage.getItem('replayTaskId')) {
                var jobName = window.localStorage.getItem('replayTaskId');
            }
            var progressUrl = "http://" + canaryEndpoint + "/plugin/drainage/replay?action=get_process&project=" + project + "&region=" + region + "&jobName=" + jobName;
            var progressRequest = new XMLHttpRequest();
            progressRequest.open('GET', progressUrl, true);
            progressRequest.send();
            progressRequest.onreadystatechange = function() { //固定写法
                var json = progressRequest.responseText; //获取到json字符串，还需解析
                var resJson = JSON.parse(json); //解析字符串---解析成object数组
                if (progressRequest.readyState === 4) {
                    if (progressRequest.status === 200) {
                        if (resJson.msg === 'succ') {
                        //获取进度，缓存下来
                        window.localStorage.setItem('replayProcess', parseInt(resJson.msg));
                        myProgress.value = parseInt(window.localStorage.getItem('replayProcess'));
                        mySpan.innerText = parseInt(window.localStorage.getItem('replayProcess')) + "%";
                        } else {
                            alert(resJson.msg);
                        }
                    } else {
                        alert('获取回放进度异常，请稍后重试！');
                    }
                }
            }
        }
        if (parseInt(window.localStorage.getItem('replayProcess')) >= 100) {
            window.localStorage.setItem('replayFinish',"true");
            // 结束回放和继续回放按钮变灰不可用,开始按钮可用
            document.getElementById("replay_stop").setAttribute("disabled", true);
            document.getElementById("replay_restart").setAttribute("disabled", true);
            document.getElementById("replay_start").removeAttribute("disabled");
            myProgress.value = 100;
            mySpan.innerText = "100%";
            clearInterval(ID);
            // 回放完成后，清空回放参数，清空缓存
            document.getElementById("replayName").options[0].selected = true;
            document.getElementById("replayRegion").options[0].selected = true;
            document.getElementById("replayType").options[0].selected = true;
            document.getElementById("replayData").options[0].selected = true;
            window.localStorage.removeItem('selectedReplayName');
            window.localStorage.removeItem('selectedReplayRegion');
            window.localStorage.removeItem('selectedReplayType');
            window.localStorage.removeItem('selectedReplayData');
            window.localStorage.removeItem('replayTaskId');
            window.localStorage.removeItem('selectedAuthType');

            window.localStorage.removeItem('replayRegionJson');
            window.localStorage.removeItem('replayTypeJson');
            window.localStorage.removeItem('replayDataJson');

            window.localStorage.removeItem('replayProcess');
            window.localStorage.removeItem('replayStatus');
            window.localStorage.removeItem('replayStartClick');
            window.localStorage.removeItem('replayStopClick');
            window.localStorage.removeItem('replayResumeClick');
            // sleep 后，将进度条设为不显示
            sleep(1200).then(() => {
                document.getElementById("myProgress").style.display = "none";
                document.getElementById("mySpan").style.display = "none";
            })
        }
    },500);
}

/**
* @Description: 点击停止按钮 ，触发暂停回放
* @Param: GET    plugin/drainage/replay?action=stop
* @return:
* @Author: yangliqin01
*/
document.getElementById('replay_stop').onclick = function() {
    // 清除点击开始回放、暂停回放操作记录
    if(window.localStorage.getItem('replayStartClick')){
        window.localStorage.removeItem('replayStartClick');
    }
    if(window.localStorage.getItem('replayResumeClick')){
        window.localStorage.removeItem('replayResumeClick');
    }
    // 用户进行了暂停回放动作的记录
    window.localStorage.setItem('replayStopClick', "true");
    // 设置未处于回放中
    window.localStorage.setItem('replayStatus', "false");
    var objReplayName = document.getElementById("replayName");
    var project = objReplayName.options[objReplayName.selectedIndex].text;
    window.localStorage.setItem("lastReplayProject", project);
    var objReplayRegion = document.getElementById("replayRegion");
    var region = objReplayRegion.options[objReplayRegion.selectedIndex].text;
    window.localStorage.setItem("lastReplayRegion", region);
    // 用于暂停回放接口的回放数据集参数jobName，使用的是从缓存读取的回放数据集
    if (window.localStorage.getItem('replayTaskId')) {
        var jobName = window.localStorage.getItem('replayTaskId');
    }
    // 对参数的异常判断，若为空提示用户选择对应参数
    var paramCheck = replayParamCheck();
    if (paramCheck == true) {
        var url = "http://" + canaryEndpoint + "/plugin/drainage/replay?action=stop&project=" + project + "&jobName=" + jobName;
        var replayStopRequest = new XMLHttpRequest();
        replayStopRequest.open('GET', url, true);
        replayStopRequest.send();
        replayStopRequest.onreadystatechange = function() {
            var json = replayStopRequest.responseText;
            // 获取到json字符串，还需解析
            var resJson = JSON.parse(json);
            // 解析字符串---解析成object数组
            if (replayStopRequest.readyState === 4) {
                if (replayStopRequest.status === 200) {
                    if (resJson.msg !== 'succ') {
                        alert(resJson.msg);
                    }
                } else {
                    alert('暂停回放任务异常，请稍后重试！');
                }
            }
        }
        // 结束引流按钮变灰不可用,开始按钮可用
        document.getElementById("replay_stop").setAttribute("disabled", true);
        document.getElementById("replay_start").setAttribute("disabled", true);
        document.getElementById("replay_restart").removeAttribute("disabled");
    } else {
        return;
    }
};
replayStopClick();

function sleep (time) {
  return new Promise((resolve) => setTimeout(resolve, time));
}

/**
* @Description: 点击按钮，触发继续回放
* @Param: GET    plugin/drainage/replay?action=resume
* @return:
* @Author: yangliqin01
*/
document.getElementById('replay_restart').onclick = function() {
    // 清除点击开始回放、暂停回放操作记录
    if (window.localStorage.getItem('replayStartClick')) {
        window.localStorage.removeItem('replayStartClick');
    }
    if (window.localStorage.getItem('replayStopClick')) {
        window.localStorage.removeItem('replayStopClick');
    }
    // 用户进行了继续回放动作的记录
    window.localStorage.setItem('replayResumeClick', "true");
    var objReplayName = document.getElementById("replayName");
    var project = objReplayName.options[objReplayName.selectedIndex].text;
    window.localStorage.setItem("lastReplayProject", project);
    var objReplayRegion = document.getElementById("replayRegion");
    var region = objReplayRegion.options[objReplayRegion.selectedIndex].text;
    window.localStorage.setItem("lastReplayRegion", region);
    // 用户进行了开始回放,从缓存中获取请求cookie值,用于回放鉴权
    var canaryCookie = 0;
    if (window.localStorage.getItem('canaryCookie')) {
        canaryCookie = window.localStorage.getItem('canaryCookie');
    }
    var objAuthType = document.getElementById("AuthType");
    var authType = objAuthType.options[objAuthType.selectedIndex].value; // 鉴权方式
    window.localStorage.setItem('selectedAuthType', objAuthType.selectedIndex);

    // 用于继续回放接口的回放数据集参数jobName，使用的是从缓存读取的回放数据集；回放任务结束后清理缓存
    if (window.localStorage.getItem('replayTaskId')) {
        var jobName = window.localStorage.getItem('replayTaskId'); //回放任务名称
    }
    // 对参数的异常判断，若为空提示用户选择对应参数
    var paramCheck = replayParamCheck();
    if (paramCheck == true) {
        // 设置处于回放中
        window.localStorage.setItem('replayStatus', "true");
        var url = "http://" + canaryEndpoint + "/plugin/drainage/replay?action=resume&project=" + project + "&jobName=" + jobName;
        var reStartRequest = new XMLHttpRequest();
        reStartRequest.open('GET', url, true);
        if (authType === 'cookie') {
            reStartRequest.setRequestHeader('canaryCookie', canaryCookie);
        }
        reStartRequest.send();
        reStartRequest.onreadystatechange = function() {
            var json = reStartRequest.responseText;
            var resJson = JSON.parse(json);
            if (reStartRequest.readyState === 4) {
                if (reStartRequest.status === 200) {
                    if (resJson.msg !== 'succ') {
                        alert(resJson.msg);
                    }
                } else {
                    alert('继续执行回放任务异常，请稍后重试！');
                }
            }
        }
        // sleep后，继续显示进度条设为不显示,等待上一个请求结束
        sleep(750).then(() => {
            showReplayProgress(project, region, jobName);
        })
        // 继续回放按钮变灰不可用,暂停按钮可用
        document.getElementById("replay_restart").setAttribute("disabled", true);
        document.getElementById("replay_start").setAttribute("disabled", true);
        document.getElementById("replay_stop").removeAttribute("disabled");
    } else {
        return;
    }
};
replayResumeClick();

// 记录点击开始回放事件，触发按钮状态变更
function replayStartClick() {
    // 没有开始的时候 结束按钮置灰
    document.getElementById("replay_stop").setAttribute("disabled", true);
    document.getElementById("replay_restart").setAttribute("disabled", true);
    // 此处需加上空判断，保证首次加载插件重复点击开始，开始按钮可用，保证点击结束后由于清空缓存无法触发
    if (window.localStorage.getItem('selectedReplayName') != 0 && window.localStorage.getItem('selectedReplayRegion') != 0 && window.localStorage.getItem('selectedReplayType') != 0 && window.localStorage.getItem('selectedReplayData') != 0 && window.localStorage.getItem('selectedAuthType') != 0) {
        if (window.localStorage.getItem('replayStartClick') == "true") {
            document.getElementById("replay_start").setAttribute("disabled", true);
            document.getElementById("replay_stop").removeAttribute("disabled");
        }
    }
}

// 记录停止开始回放事件，触发按钮状态变更
function replayStopClick() {
    if (window.localStorage.getItem('selectedReplayName') != 0 && window.localStorage.getItem('selectedReplayRegion') != 0 && window.localStorage.getItem('selectedReplayType') != 0 && window.localStorage.getItem('selectedReplayData') != 0 && window.localStorage.getItem('selectedAuthType') != 0) {
        if(window.localStorage.getItem('replayStopClick') == "true"){
            document.getElementById("replay_stop").setAttribute("disabled", true);
            document.getElementById("replay_restart").removeAttribute("disabled");
        }
    }
}

// 记录点击继续回放事件，触发按钮状态变更
function replayResumeClick() {
    if (window.localStorage.getItem('selectedReplayName') != 0 && window.localStorage.getItem('selectedReplayRegion') != 0 && window.localStorage.getItem('selectedReplayType') != 0 && window.localStorage.getItem('selectedReplayData') != 0 && window.localStorage.getItem('selectedAuthType') != 0) {
        if(window.localStorage.getItem('replayResumeClick') == "true"){
            document.getElementById("replay_restart").setAttribute("disabled", true);
            document.getElementById("replay_stop").removeAttribute("disabled");
        }
    }
}

// 回放任务，点击开始、结束、继续按钮,对参数的异常判断，若为空提示用户选择对应参数
function replayParamCheck() {
    if (window.localStorage.getItem('selectedReplayName') == null || window.localStorage.getItem('selectedReplayName') == 0) {
        alert("项目参数为空，请在列表选择项目！");
        return false;
    }
    if (window.localStorage.getItem('selectedReplayRegion') == 0) {
        alert("地域参数为空，请在列表选择地域！");
        return false;
    }
    if (window.localStorage.getItem('selectedReplayType') == 0) {
        alert("回放环境为空，请在列表选择回放环境！");
        return false;
    }
    if (window.localStorage.getItem('selectedReplayData') == 0) {
        alert("回放数据集为空，请在列表选择回放环境！");
        return false;
    }
    if (window.localStorage.getItem('selectedAuthType') == 0) {
        alert("鉴权方式为空，请在列表选择鉴权方式！");
        return false;
    }
    return true;
}
/**
* @Description: 检查是否处于回放中，若处于回放中实时刷新进度条，保证退出插件后仍实时更新
* @Param:
* @return: 
* @Author: yangliqin01
*/
function checkReplayProcess() {
    if (window.localStorage.getItem('replayStatus')) {
        document.getElementById("replay_start").setAttribute("disabled", true);
    }
    // 判断回放任务结束时，设置进度条为100%，保证退出插件场景
    if (window.localStorage.getItem('replayFinish') == "true") {
        window.localStorage.removeItem('selectedReplayName');
        window.localStorage.removeItem('selectedReplayRegion');
        window.localStorage.removeItem('selectedReplayType');
        window.localStorage.removeItem('selectedReplayData');
        window.localStorage.removeItem('selectedAuthType');
        window.localStorage.removeItem('replayTaskId');
        document.getElementById("myProgress").value = 100;
        document.getElementById("mySpan").innerText = "100%";
        window.localStorage.removeItem('replayFinish');
    }
    var objReplayName = document.getElementById("replayName");
    var project = objReplayName.options[objReplayName.selectedIndex].text;
    var objReplayRegion = document.getElementById("replayRegion");
    var region = objReplayRegion.options[objReplayRegion.selectedIndex].text;
    if (window.localStorage.getItem('replayTaskId')) {
        var jobName = window.localStorage.getItem('replayTaskId');
    }
    if (window.localStorage.getItem('replayStatus') == "true") {
        showReplayProgress(project, region, jobName);
    } else {
        if(window.localStorage.getItem('replayProcess') !=0 && window.localStorage.getItem('replayProcess') != null){
            document.getElementById("myProgress").style.display = "inline";
            document.getElementById("mySpan").style.display = "inline";
        }
    }
}