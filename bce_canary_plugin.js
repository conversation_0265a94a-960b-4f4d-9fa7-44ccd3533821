/**
 * @file 百度云线上灰度测试的chrome插件实现，修改X-Bce-Request-Id和_xxx_version_:*******到sessionStorage中
 * <AUTHOR>
 */
// 根据local storage里的参数、自动化设置request id
function generateRequestId() {
    function chunk() {
        let v = (~~(Math.random() * 0xffff)).toString(16);
        if (v.length < 4) {
            v += new Array(4 - v.length + 1).join('0');
        }
        return v;
    }

    const a = chunk();
    const b = chunk();
    const c = chunk();
    const d = chunk();
    const e = chunk();
    const f = chunk();
    const g = chunk();
    const h = chunk();

    var projectTag = window.localStorage.getItem('projectTag');
    var envTag = window.localStorage.getItem('envTag');
    var diverterTag = window.localStorage.getItem('diverterTag');

    var bceCanarySwitch = window.localStorage.getItem('bceCanarySwitch');
    if (bceCanarySwitch === 'on') {
        return a + b + '-' + c + '-' + envTag + '-' + projectTag + '-' + diverterTag;
    } else {
        return a + b + '-' + c + '-' + d + '-' + e + '-' + f + g + h;
    }
}

// 监听并获取请求的cookie值
function getSession(resultsArray) {
    if (!resultsArray) {
        return;
    }
    window.localStorage.setItem('canaryCookie', resultsArray[0]);
}

// 监听web request、自动化为每个请求添加http头：X-Bce-Request-Id
chrome.webRequest.onBeforeSendHeaders.addListener(function (details) {
    details.requestHeaders.push({name: 'X-Bce-Request-Id', value: generateRequestId()});
    return {requestHeaders: details.requestHeaders};
}, {urls: ['<all_urls>']}, ['requestHeaders', 'blocking']);

// 因为sessionStorage是每个chrome tab页独特的，因此需根据tab id执行命令添加，实现整套方案真累死我了
function setFeVersionToSessionStroage(tabId) {
    /* global chrome */
    chrome.tabs.executeScript(tabId, {code: 'document.cookie;'}, getSession);

    var feVersionKey = [];
    var feVersionValue = [];
    // 从local storage中获取fe version进行格式判断，其格式为_xxx_version_:*******
    var feVersion = window.localStorage.getItem('feVersion');
    if ((feVersion != null) && (feVersion.length > 3)) {
        var multiProjectList = window.localStorage.getItem('feVersion').split(',');
        for (var i = 0; i < multiProjectList.length; i++) {
            var feVersionList = multiProjectList[i].split(':');
            if (feVersionList.length === 2) {
                feVersionKey.push(feVersionList[0]);
                feVersionValue.push(feVersionList[1]);
            } else {
                return;
            }
        }
    } else {
        return;
    }

    // 只有fe version设置正确、开关为on状态，才向session storage设置fe版本号
    var bceCanarySwitch = window.localStorage.getItem('bceCanarySwitch');
    if (bceCanarySwitch === 'on') {
        if (tabId) {
            var i = 0;
            while (i < feVersionValue.length && feVersionValue[i] !== 'delete') {
                var executeCode = 'window.sessionStorage.setItem(\'' + feVersionKey[i]
                + '\', \'' + feVersionValue[i] + '\');';
                chrome.tabs.executeScript(tabId, {code: executeCode});
                i++;
            }
            // return;
        }
    }
    // 按钮为off 并且FeVersion有值的情况
    if (bceCanarySwitch === 'off') {
        if (tabId) {
            var i = 0;
            while (i < feVersionValue.length && feVersionValue[i] !== 'delete') {
                var executeCode = 'window.sessionStorage.setItem(\''
                + feVersionKey[i] + '\', \'\');';
                /* global chrome */
                chrome.tabs.executeScript(tabId, {code: executeCode});
                i++;
            }
        }
    }
    // 如果之前有设置fe version，但开关不是on状态、或者现在fe version被置为了空、则向session storage中设置为空
    var i = 0;
    while (i < feVersionKey.length && feVersionValue[i] === 'delete') {
        var executeCode = 'window.sessionStorage.setItem(\'' + feVersionKey[i] + '\', \'\');';
        /* global chrome */
        chrome.tabs.executeScript(tabId, {
            code: executeCode
        });
        i++;
    }
}

// 监听tab激活信息
chrome.tabs.onActivated.addListener(function (activeInfo) {
    setFeVersionToSessionStroage(activeInfo.tabId);
});

// 监听tab创建
chrome.tabs.onCreated.addListener(function (tab) {
    setFeVersionToSessionStroage(tab.id);
});

// 监听tab更新
chrome.tabs.onUpdated.addListener(function (tabId, changeInfo, tab) {
    setFeVersionToSessionStroage(tabId);

    setFeVersionToSessionStroage(tab.id);
});

// 监听tab移动
chrome.tabs.onMoved.addListener(function (tabId, moveInfo) {
    setFeVersionToSessionStroage(tabId);
});

// 监听tab高亮
chrome.tabs.onHighlighted.addListener(function (highlightInfo) {
    var i = 0;
    var len = highlightInfo.tabIds.length;
    for (; i < len; i++) {
        if (highlightInfo.tabIds[i]) {
            setFeVersionToSessionStroage(highlightInfo.tabIds[i]);
        }
    }
});

// 监听tab从当前窗口分离
chrome.tabs.onDetached.addListener(function (tabId, detachInfo) {
    setFeVersionToSessionStroage(tabId);
});

// 监听tab加载到当前窗口
chrome.tabs.onAttached.addListener(function (tabId, attachInfo) {
    setFeVersionToSessionStroage(tabId);
});
